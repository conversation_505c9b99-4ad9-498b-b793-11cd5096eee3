import {createContext, useContext} from "react";
import {NhomPhanCapDuyetContextProps} from "./index.model";
export const NhomPhanCapDuyetContext = createContext<NhomPhanCapDuyetContextProps>({
  loading: false,
  tongSoDong: 0,
  filterParams: {},
  danhSachNhomPhanCapDuyetPhanTrang: [],
  setFilterParams: () => {},
  layDanhSachNhomPhanCapDuyetPhanTrang: () => {},
  onUpdateNhomPhanCapDuyet: () => Promise.resolve(null),
  layChiTietNhomPhanCapDuyet: () => Promise.resolve(null),
});
export const useNhomPhanCapDuyetContext = () => useContext(NhomPhanCapDuyetContext);
