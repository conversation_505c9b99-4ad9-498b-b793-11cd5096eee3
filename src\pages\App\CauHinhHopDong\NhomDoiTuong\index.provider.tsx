import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";
import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {NhomDoiTuongContext} from "./index.context";
import {NhomDoiTuongContextProps} from "./index.model";

/* 
  file Provider : Chứa logic quản lý state, gọi API, wrap context
*/

const NhomDoiTuongProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [danhSachNhomDoiTuong, setDanhSachNhomDoiTuong] = useState<Array<CommonExecute.Execute.INhomDoiTuong>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [chiTietNhomDoiTuong, setChiTietNhomDoiTuong] = useState<CommonExecute.Execute.IChiTietNhomDoiTuong | null>(null);
  // const [listNhomDoiTuongCT, setListNhomDoiTuongCT] = useState<Array<CommonExecute.Execute.IChiTietNhomDoiTuong>>([]);
  const [listNhomDoiTuongCT, setListNhomDoiTuongCT] = useState<Array<NonNullable<CommonExecute.Execute.IChiTietNhomDoiTuong["lke_ct"]>[0]>>([]);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangNhomDoiTuongParams & ReactQuery.IPhanTrang>({
    ma: "",
    ten: "",
    trang_thai: "",
    trang: 1,
    // so_dong: ,
  });
  useEffect(() => {
    initData();
  }, []);

  const initData = () => {
    layDanhSachNhomDoiTuong(filterParams);
    getListDoiTac();
  };
  useEffect(() => {
    layDanhSachNhomDoiTuong(filterParams);
  }, [filterParams]);
  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);

  //DS đại lý phân trang
  const layDanhSachNhomDoiTuong = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangNhomDoiTuongParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_NHOM_DOI_TUONG,
        };

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data || [];
        const tongSoDong = response.data.tong_so_dong || 0;

        // setDanhSachNhomDoiTuong(data);
        // setTongSoDong(response.data.tong_so_dong);
        return {
          data,
          tong_so_dong: tongSoDong,
        };
      } catch (error: any) {
        console.log("Lấy danh sách nhóm đối tượng error:", error.message || error);
        return {
          data: [],
          tong_so_dong: 0,
        };
      }
    },
    [mutateUseCommonExecute],
  );
  //DS nhóm đối tượng phân trang
  // const layDanhSachNhomDoiTuongCha = useCallback(
  //   async (body: ReactQuery.ITimKiemPhanTrangDanhSachNhomDoiTuongParams & ReactQuery.IPhanTrang) => {
  //     try {
  //       const params = {
  //         ...body,
  //         actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DS_DAI_LY,
  //       };

  //       const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
  //       const data = response.data.data;
  //       retur
  //       // setDanhSachNhomDoiTuong(data);
  //       // setTongSoDong(response.data.tong_so_dong);
  //     } catch (error: any) {
  //       console.log("Lấy danh sách đại lý error ", error.message | error);
  //     }
  //   },
  //   [mutateUseCommonExecute],
  // );
  //Lấy chi tiết 1 đại lý
  const layChiTietNhomDoiTuong = useCallback(
    async (item: ReactQuery.IChiTietNhomDoiTuongParams): Promise<CommonExecute.Execute.IChiTietNhomDoiTuong | null> => {
      try {
        const params = {
          ma: item.ma,
          actionCode: ACTION_CODE.CHI_TIET_NHOM_DOI_TUONG,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const payload: any = (responseData.data as any)?.data ?? responseData.data;
        const lke = payload?.lke;
        const response = lke ? ((Array.isArray(lke) ? lke[0] : Object.values(lke)[0]) as CommonExecute.Execute.IChiTietNhomDoiTuong) : null;
        console.log("responseData.data", responseData);
        setChiTietNhomDoiTuong(response);
        const lkeCt = (payload?.lke_ct ?? response?.lke_ct ?? []) as Array<NonNullable<CommonExecute.Execute.IChiTietNhomDoiTuong["lke_ct"]>[0]>;
        setListNhomDoiTuongCT(lkeCt);
        console.log("list nhóm đối tượng chi tiết", lkeCt);
        return response;
      } catch (error: any) {
        console.log("layChiTiet nhóm đối tượng error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  //Cập nhật hoặc tạo mới 1 đại lý
  const onUpdateNhomDoiTuong = useCallback(
    async (body: ReactQuery.IUpdateNhomDoiTuongParams): Promise<number | null | undefined> => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_NHOM_DOI_TUONG,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          initData();
          // Chuyển đổi responseData.data thành number
          return responseData.data as unknown as number; // Chuyển đổi responseData.data thành number
        }
      } catch (error: any) {
        console.log("onUpdateNhomDoiTuong error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Xóa thuộc tính nhóm đối tượng
  const onDeleteNhomDoiTuongCT = useCallback(
    async (body: ReactQuery.IDeleteNhomDoiTuongCTParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.XOA_THUOC_TINH_NHOM_DOI_TUONG,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Xóa thông tin thành công!");
          initData();
          return responseData.data as unknown as number; // Chuyển đổi responseData.data thành number
        }
      } catch (error: any) {
        console.log("onDeleteNhomDoiTuongCT error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<NhomDoiTuongContextProps>(
    () => ({
      listDoiTac,
      tongSoDong,
      danhSachNhomDoiTuong,
      loading: mutateUseCommonExecute.isLoading,
      chiTietNhomDoiTuong,
      listNhomDoiTuongCT,
      filterParams,
      onDeleteNhomDoiTuongCT,
      setFilterParams,
      getListDoiTac,
      onUpdateNhomDoiTuong,
      layDanhSachNhomDoiTuongPhanTrang: layDanhSachNhomDoiTuong,
      layChiTietNhomDoiTuong,
    }),
    [
      danhSachNhomDoiTuong,
      mutateUseCommonExecute,
      filterParams,
      listNhomDoiTuongCT,
      chiTietNhomDoiTuong,
      tongSoDong,
      listDoiTac,
      onDeleteNhomDoiTuongCT,
      setFilterParams,
      onUpdateNhomDoiTuong,
      layDanhSachNhomDoiTuong,
      layChiTietNhomDoiTuong,
      getListDoiTac,
    ],
  );

  return <NhomDoiTuongContext.Provider value={value}>{children}</NhomDoiTuongContext.Provider>;
};

export default NhomDoiTuongProvider;
