import {ReactQuery} from "@src/@types";
import {TableTaiKhoanDonViThuHoColumnDataType} from "./index.configs";

export interface ICauHinhHuongHoaHongContextProps {
  listTaiKhoanDonViThuHo: Array<CommonExecute.Execute.ITaiKhoanDonViThuHo>;
  listDonVi: Array<CommonExecute.Execute.IDanhSachDonViBHXH>;
  listLoaiHoGiaDinh: Array<CommonExecute.Execute.ILoaiHoGiaDinh>;
  tongSoDong: number;
  tongSoDongCT: number;
  loading: boolean;
  filterParams: ReactQuery.ITimKiemPhanTrangTaiKhoanDonViThuHoParams & ReactQuery.IPhanTrang;
  chiTietTaiKhoanDonViThuHo: CommonExecute.Execute.ITaiKhoanDonViThuHo;
  danhSachCauHinhHuongHoaHongNgayApDung: Array<CommonExecute.Execute.ICauHinhHuongHoaHongNgayApDung>;
  danhSachCauHinhHuongHoaHong: Array<CommonExecute.Execute.ICauHinhHuongHoaHong>;
  chiTietCauHinhHuongHoaHong: CommonExecute.Execute.ICauHinhHuongHoaHong;
  xoaCauHinhHuongHoaHong: (params: ReactQuery.IUpdateCauHinhHuongHoaHongParams) => Promise<boolean>;
  layChiTietCauHinhHuongHoaHong: (params: ReactQuery.IChiTietCauHinhHuongHoaHongParams) => Promise<CommonExecute.Execute.ICauHinhHuongHoaHong>;
  layDanhSachCauHinhHuongHoaHong: (params: ReactQuery.ITimKiemPhanTrangCauHinhHuongHoaHongParams) => Promise<any>;
  layDanhSachCauHinhHuongHoaHongNgayApDung: (params: ReactQuery.ILietKeCauHinhHuongHoaHongNgayApDungParams) => Promise<any>;
  updateCauHinhHuongHoaHongNgayApDung: (params: ReactQuery.IUpdateCauHinhHuongHoaHongNgayApDungParams) => Promise<boolean>;
  getListTaiKhoanDonViThuHo: (params?: ReactQuery.ITimKiemPhanTrangTaiKhoanDonViThuHoParams & ReactQuery.IPhanTrang) => Promise<void>;
  getChiTietTaiKhoanDonViThuHo: (params: TableTaiKhoanDonViThuHoColumnDataType) => Promise<CommonExecute.Execute.ITaiKhoanDonViThuHo>;
  xoaNgayApDungCauHinhHuongHoaHong: (params: ReactQuery.IUpdateCauHinhHuongHoaHongNgayApDungParams) => Promise<boolean>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangTaiKhoanDonViThuHoParams & ReactQuery.IPhanTrang>>;
  capNhatCauHinhHuongHoaHong: (params: ReactQuery.IUpdateCauHinhHuongHoaHongParams) => Promise<boolean>;
}
