import {CloseOutlined, DownloadOutlined, InfoCircleOutlined, PlusCircleOutlined, PlusOutlined, UploadOutlined, WarningOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, ModalImportExcel} from "@src/components";
import type {IModalImportExcelRef, IExcelRowData} from "@src/components/ModalImportExcel";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty, useAsyncAction} from "@src/hooks";
import {formatCurrencyUS, formatDateTime, parseDateTime} from "@src/utils";
import {Col, Form, message, Row, Table, Tabs} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {Dayjs} from "dayjs";
import {useBaoHiemTaiSanContext} from "../../index.context";
import {ruleRequired, ThongTinDoiTuongBaoHiemTaiSanStepProps} from "../Constant";
import {
  defaultFormValueTimKiemDoiTuongBaoHiemTaiSan,
  doiTuongCTColumns,
  FormTaoMoiDoiTuongBaoHiemTaiSan,
  FormTimKiemDoiTuongBaoHiemTaiSan,
  IModalDanhSachDoiTuongTaiSanRef,
  initFormFieldsDoiTuong,
  // listHDVipSelect,
  tableDoiTuongColumn,
  TableDoiTuongColumnDataType,
  TableDoiTuongCTDataType,
  tableTaiSanColumn,
  VI_TRI,
} from "./Constant";
import {RenderTableDanhSachTaiSan} from "./RenderTableDanhSachTaiSan";
import {ModalDanhSachDoiTuong} from "./ModalDanhSachDoiTuongTaiSan";
// import {RenderDieuKhoanBoSungTable, RenderLoaiHinhNghiepVuTable} from "./index";

const {nd_tim} = FormTimKiemDoiTuongBaoHiemTaiSan;
const {ten, gcn, nhom_dt, latitude, longitude, gio_hl, gia_tri, ngay_hl, gio_kt, ngay_kt, ngay_cap, vip, so_id_dt, so_id, dk, dkbs, vi_tri, tinh_thanh, phuong_xa, dchi, dt_ttrr} =
  FormTaoMoiDoiTuongBaoHiemTaiSan;

const PAGE_SIZE = 10; // khai báo khác default cho vừa màn hình
const ThongTinDoiTuongBaoHiemTaiSanStepComponent = forwardRef<any, ThongTinDoiTuongBaoHiemTaiSanStepProps>(
  ({formNhapDoiTuongBaoHiemTaiSan: formNhapDoiTuongBaoHiemTaiSan}: ThongTinDoiTuongBaoHiemTaiSanStepProps, ref) => {
    useImperativeHandle(ref, () => ({
      resetForm: () => {},
    }));

    const {
      loading,
      tongSoDongDoiTuongBaoHiemTaiSan,
      tongPhiBaoHiemTaiSanFromAPI,
      danhSachDoiTuongBaoHiemTaiSan,
      timKiemPhanTrangDoiTuongBaoHiemTaiSan,
      getListPhuongXa,
      setListPhuongXa,
      listTinhThanh,
      listPhuongXa,
      // layChiTietDoiTuongBaoHiemTaiSan,
      chiTietDoiTuongBaoHiemTaiSan,
      chiTietHopDongBaoHiemTaiSan,
      listNhomDoiTuong,
      updateDoiTuongBaoHiemTaiSan,
      layChiTietDoiTuongBaoHiemTaiSan,
      timKiemPhanTrangDanhSachTaiSan,
      timKiemPhanTrangDanhSachDoiTuong,
      danhSachDoiTuong,
      // exportExcel,
    } = useBaoHiemTaiSanContext();
    const [formTimKiemPhanTrangDoiTuongBaoHiemTaiSan] = Form.useForm();

    const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams>(
      defaultFormValueTimKiemDoiTuongBaoHiemTaiSan as ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams,
    );
    const pageSizeCT = 4;
    const [inputRowKeys, setInputRowKeys] = useState<string[]>([]);
    const [dataSource, setDataSource] = useState<Array<TableDoiTuongCTDataType>>([]);
    const [inputRowKey, setInputRowKey] = useState<string | null>(null);
    const [page, setPage] = useState(1);
    const [doiTuongTaiSanSelected, setDoiTuongTaiSanSelected] = useState<CommonExecute.Execute.IDoiTuongBaoHiemTaiSan | null>(null);
    const watchViTri = Form.useWatch("vi_tri", formNhapDoiTuongBaoHiemTaiSan);

    const watchNhomDoiTuong = Form.useWatch("nhom_dt", formNhapDoiTuongBaoHiemTaiSan);
    const watchDTTTRR = Form.useWatch("dt_ttrr", formNhapDoiTuongBaoHiemTaiSan);
    const [doiTuongSelected, setDoiTuongSelected] = useState<CommonExecute.Execute.IDoiTuong | null>(null);
    // Modal Import Excel ref
    const modalImportExcelRef = useRef<IModalImportExcelRef>(null);
    const refModelDanhSachDoiTuong = useRef<IModalDanhSachDoiTuongTaiSanRef>(null);
    const [currentInputField, setCurrentInputField] = useState<string>("");

    // Xử lý khi chi tiết đối tượng thay đ
    useEffect(() => {
      if (chiTietDoiTuongBaoHiemTaiSan) {
        const data = danhSachDoiTuong.find(item => item.ma === chiTietDoiTuongBaoHiemTaiSan.gcn?.dt_ttrr);
        if (data) {
          setDoiTuongSelected({
            ma: data.ma,
            ten: data.ten,
          });
        } else {
          setDoiTuongSelected(null);
        }

        // // Load danh sách phuong_xa nếu có tinh_thanh
        if (chiTietDoiTuongBaoHiemTaiSan?.gcn?.tinh_thanh) {
          getListPhuongXa({ma_tinh: chiTietDoiTuongBaoHiemTaiSan.gcn.tinh_thanh});
        }

        formNhapDoiTuongBaoHiemTaiSan.setFieldValue("dt_ttrr", data?.ma);
      }
    }, [chiTietDoiTuongBaoHiemTaiSan, formNhapDoiTuongBaoHiemTaiSan, danhSachDoiTuong]);
    useEffect(() => {
      if (chiTietDoiTuongBaoHiemTaiSan) {
        initFormFieldsDoiTuong(formNhapDoiTuongBaoHiemTaiSan, chiTietDoiTuongBaoHiemTaiSan);
        timKiemPhanTrangDanhSachDoiTuong({trang: 1, so_dong: 10000, ten: ""});
      }
    }, [chiTietDoiTuongBaoHiemTaiSan, formNhapDoiTuongBaoHiemTaiSan]);

    // Xử lý phuong_xa sau khi danh sách phuong_xa đã được load
    useEffect(() => {
      if (chiTietDoiTuongBaoHiemTaiSan && listPhuongXa.length > 0) {
        const danhSachPhuongXa = listPhuongXa.find(item => item.ma === chiTietDoiTuongBaoHiemTaiSan?.gcn?.phuong_xa);
        if (danhSachPhuongXa) {
          formNhapDoiTuongBaoHiemTaiSan.setFieldValue("phuong_xa", danhSachPhuongXa.ma);
        }
      }
    }, [chiTietDoiTuongBaoHiemTaiSan, listPhuongXa, formNhapDoiTuongBaoHiemTaiSan]);

    useEffect(() => {
      //nếu giá trị thuộc tính kiểu datime thì đổi dữ liệu trường giá trị thành định dạng YYYYMMDD
      const newData = dataSource.map(item => {
        if (item.kieu_dl === "DATE") {
          return {...item, gia_tri: formatDateTime(item.gia_tri)};
        }
        return item;
      });
      const validData = newData
        .filter(item => !item.key.includes("empty") && item.ma_thuoc_tinh?.trim())
        .map(item => ({
          ma: item.ma_thuoc_tinh,
          gia_tri: item.gia_tri,
        }));

      formNhapDoiTuongBaoHiemTaiSan.setFieldValue("ttinh", validData);
    }, [dataSource, formNhapDoiTuongBaoHiemTaiSan]);
    // Hàm dùng chung để chọn đối tượng bảo hiểm tài sản
    const handleSelectDoiTuongBaoHiemTaiSan = useCallback(
      async (record: any) => {
        if (!record.so_id || (record.key && record.key.toString().includes("empty"))) return;
        const response = await layChiTietDoiTuongBaoHiemTaiSan(record as ReactQuery.IChiTietDoiTuongBaoHiemTaiSanParams);
        if (response) initFormFieldsDoiTuong(formNhapDoiTuongBaoHiemTaiSan, response);
        setDoiTuongTaiSanSelected(response || null);
        await timKiemPhanTrangDanhSachTaiSan({so_id: chiTietHopDongBaoHiemTaiSan?.so_id, so_id_dt: response?.gcn?.so_id_dt, trang: 1, so_dong: 10});
      },
      [layChiTietDoiTuongBaoHiemTaiSan, formNhapDoiTuongBaoHiemTaiSan, chiTietHopDongBaoHiemTaiSan],
    );
    const handleChangeTinhThanh = useCallback(
      (value: string) => {
        formNhapDoiTuongBaoHiemTaiSan.setFieldValue("phuong_xa", undefined);
        getListPhuongXa({ma_tinh: value});
      },
      [getListPhuongXa],
    );
    const handleChangeViTri = useCallback(
      (value: string) => {
        formNhapDoiTuongBaoHiemTaiSan.setFieldValue("tinh_thanh", undefined);
        formNhapDoiTuongBaoHiemTaiSan.setFieldValue("phuong_xa", undefined);
      },
      [formNhapDoiTuongBaoHiemTaiSan],
    );
    //khi thay đổi nhóm đối tượng thì reset các trường đối tượng rủi ro và đối tượng
    const handleChangeNhomDoiTuong = useCallback(() => {
      formNhapDoiTuongBaoHiemTaiSan.setFieldValue("dt_ttrr", undefined);
      formNhapDoiTuongBaoHiemTaiSan.setFieldValue("ten", undefined);
      formNhapDoiTuongBaoHiemTaiSan.setFieldValue("dchi", undefined);
      formNhapDoiTuongBaoHiemTaiSan.setFieldValue("tinh_thanh", undefined);
      formNhapDoiTuongBaoHiemTaiSan.setFieldValue("phuong_xa", undefined);
      formNhapDoiTuongBaoHiemTaiSan.setFieldValue("latitude", undefined);
      formNhapDoiTuongBaoHiemTaiSan.setFieldValue("longitude", undefined);
      if (watchNhomDoiTuong === "NHA_CUA") {
        formNhapDoiTuongBaoHiemTaiSan.setFieldValue("vi_tri", "C");
        // formNhapDoiTuongBaoHiemTaiSan.setFieldValue("tinh_thanh", " ");
      }
    }, [formNhapDoiTuongBaoHiemTaiSan]);
    //DATA TABLE ĐỐI TƯỢNG BB
    const dataTableListDoiTuongBaoHiemTaiSan = useMemo<Array<TableDoiTuongColumnDataType>>(() => {
      try {
        const tableData = danhSachDoiTuongBaoHiemTaiSan.map((item: any, index: number) => ({
          ...item,

          key: index.toString(),
        }));
        //nếu có dữ liệu thì chọn đối tượng đầu tiên trong mảng
        if (tableData.length > 0 && !doiTuongTaiSanSelected) {
          handleSelectDoiTuongBaoHiemTaiSan(tableData[0]);
          setDoiTuongTaiSanSelected(tableData[0]);
        } else if (tableData.length > 0 && doiTuongTaiSanSelected) {
          const currentSelected = tableData.find(item => item.so_id_dt === doiTuongTaiSanSelected?.gcn?.so_id_dt);
          if (currentSelected) {
            handleSelectDoiTuongBaoHiemTaiSan(currentSelected);
          } else {
            handleSelectDoiTuongBaoHiemTaiSan(tableData[0]);
          }
        }
        // Add empty rows if data is less than 10 rows
        const arrEmptyRow: Array<TableDoiTuongColumnDataType> = fillRowTableEmpty(tableData.length, PAGE_SIZE);
        return [...tableData, ...arrEmptyRow];
      } catch (error) {
        console.log("dataTableListPhongBan error", error);
        return [];
      }
    }, [danhSachDoiTuongBaoHiemTaiSan]);

    //hàm tìm kiếm phân trang
    const handleSearchAndPagination = useCallback(
      (values?: ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams & Partial<ReactQuery.IPhanTrang>, pageArg?: number) => {
        const so_id = chiTietHopDongBaoHiemTaiSan?.so_id;
        // if là bấm tìm kiếm else là onchange page
        if (values) {
          const cleanedValues = {
            ...values,
            nd_tim: values.nd_tim ?? "",
            dong_tai: values.dong_tai ?? "",
            so_id,
          };
          setSearchParams(cleanedValues);
          setPage(1);
          timKiemPhanTrangDoiTuongBaoHiemTaiSan({...cleanedValues, trang: 1, so_dong: PAGE_SIZE});
        } else {
          const page = pageArg || 1;
          setPage(page);
          timKiemPhanTrangDoiTuongBaoHiemTaiSan({
            ...searchParams,
            so_id,
            trang: page,
            so_dong: PAGE_SIZE,
          });
        }
      },
      [chiTietHopDongBaoHiemTaiSan, searchParams],
    );

    // Handle export Excel using useAsyncAction //Tạo 1 biến isSubmiting riêng cho từng action
    const [handleExportExcel, isExportingExcel] = useAsyncAction(async () => {
      const so_id = chiTietHopDongBaoHiemTaiSan?.so_id;
      if (!so_id) {
        message.error("Không có thông tin hợp đồng để xuất Excel!");
        return;
      }

      try {
        // await exportExcel({so_id});
      } catch (error) {
        console.error("Export Excel error:", error);
        message.error("Có lỗi xảy ra khi xuất Excel!");
        throw error; // Re-throw để useAsyncAction có thể handle
      }
    });

    const dataTableListDoiTuongCT = useMemo<Array<TableDoiTuongCTDataType>>(() => {
      try {
        const mappedData = Array.isArray(chiTietDoiTuongBaoHiemTaiSan?.gcn_ct)
          ? chiTietDoiTuongBaoHiemTaiSan?.gcn_ct
              .filter(item => item)
              .map((item: any, index: number) => ({
                key: index.toString(),
                stt: index + 1,
                ma_thuoc_tinh: item.ma_thuoc_tinh,
                ten_thuoc_tinh: item.ten_thuoc_tinh,
                kieu_dl: item.kieu_dl,
                gia_tri: item.gia_tri,
              }))
          : [];

        const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSizeCT);

        setDataSource([...mappedData, ...arrEmptyRow]);

        return [...mappedData, ...arrEmptyRow];
      } catch (error) {
        console.error("Lỗi khi xử lý dữ liệu bảng ngày áp dụng:", error);
        return [];
      }
    }, [chiTietDoiTuongBaoHiemTaiSan?.gcn_ct]);

    // Handle import Excel
    const handleImportExcel = useCallback(() => {
      modalImportExcelRef.current?.openFilePicker();
    }, []);

    // Handle confirmed data from Excel import
    const handleExcelDataConfirm = useCallback((data: IExcelRowData[]) => {
      console.log("Excel data confirmed:", data);
      message.success(`Đã import thành công ${data.length} dòng dữ liệu từ Excel!`);

      // TODO: Process the imported data
      // You can:
      // 1. Add the data to the current table
      // 2. Send to server for batch processing
      // 3. Show confirmation dialog
      // 4. Update the form with imported data

      // Example: Log sample data for debugging
      if (data.length > 0) {
        console.log("Sample imported row:", data[0]);
      }

      // Refresh the table data after import
      handleSearchAndPagination();
    }, []);
    const handleInputChange = (index: number, dataIndex: string, value: string) => {
      setDataSource(prev => {
        const next = [...prev];
        next[index] = {...next[index], [dataIndex]: value};

        //nếu giá trị thuộc tính kiểu datime thì đổi dữ liệu trường giá trị thành định dạng dd/mm/yyyy
        // if (dataIndex === "gia_tri" && next[index].kieu_dl === "DATE") {
        //   next[index] = {...next[index], [dataIndex]: formatDateTime(value)};
        // }
        return next;
      });
    };
    // Handle cancel import
    const handleExcelImportCancel = useCallback(() => {
      console.log("Excel import cancelled");
    }, []);

    // RENDER
    const renderFormInputColum = (props?: any, span = 4) => {
      return (
        <Col span={span}>
          <FormInput {...props} />
        </Col>
      );
    };
    const renderColumn = (column: any, index: number) => {
      if (column.dataIndex === "gia_tri") {
        return {
          ...column,
          render: (_: any, record: TableDoiTuongCTDataType, index: number) => {
            const isEmptyRow = record.key.includes("empty");
            const kieuDuLieu = record.kieu_dl;

            // if (isEmptyRow) return <div style={{height: 22}} />;
            // Dòng thường thì render như cũ
            if (isEmptyRow) {
              return (
                <div className="custom-checkbox-cell">
                  <FormInput className="!mb-0 h-[30px]" component="select" disabled style={{display: "none"}} />
                </div>
              );
            }

            // Render cho dòng có dữ liệu (không đang edit)
            // Xác định component và className dựa trên kiểu dữ liệu
            let component = "input";
            let className = "text-left";

            if (kieuDuLieu === "DATE") {
              component = "date-picker";
              className = "text-left";
              return (
                <div className="custom-checkbox-cell">
                  <FormInput
                    style={{border: "none", borderBottom: "1px solid #96bf49", borderRadius: 0}}
                    className="custom-input-no-radius !mb-0"
                    component="date-picker"
                    value={(parseDateTime(record[column.dataIndex as keyof TableDoiTuongCTDataType]) || undefined) as any}
                    onChange={(value: any) => handleInputChange(index, column.dataIndex as string, value?.target?.value ?? value)}
                    autoFocus
                  />
                </div>
              );
            } else if (kieuDuLieu === "NUMBER") {
              // component = "input";
              // className = "text-right";
              return (
                <div className="custom-checkbox-cell text-right">
                  <FormInput
                    className={"custom-input-no-radius !mb-0 h-[30px] !pb-0 !text-right"}
                    component="input"
                    style={{textAlign: "end", border: "none", borderBottom: "1px solid #96bf49", borderRadius: 0}}
                    value={record[column.dataIndex as keyof TableDoiTuongCTDataType] ?? ""}
                    onChange={(value: any) => {
                      // Nếu value là event, lấy value.target.value, còn lại lấy trực tiếp
                      const newValue = value && value.target ? value.target.value : value;
                      handleInputChange(index, column.dataIndex as string, newValue);
                    }}
                    type="number"
                  />
                </div>
              );
            } else {
              component = "input";
              className = "text-left ";
              return (
                <FormInput
                  style={{border: "none", borderBottom: "1px solid #96bf49", borderRadius: 0}}
                  className={"custom-input-no-radius !mb-0 h-[30px] !pb-0"}
                  component="input"
                  value={(record[column.dataIndex as keyof TableDoiTuongCTDataType] ?? "") as any}
                  onChange={(value: any) => {
                    // Nếu value là event, lấy value.target.value, còn lại lấy trực tiếp
                    const newValue = value && value.target ? value.target.value : value;
                    handleInputChange(index, column.dataIndex as string, newValue);
                  }}
                />
              );
            }
          },
        };
      }
      return {
        ...column,
        // ...(column.key && typeof column.title === "string" && column.key !== "stt" ? getColumnSearchDoiTuongCTProps(column.key, column.title) : {}),
        render: (text: string) => text || "", // Đảm bảo hiển thị chuỗi rỗng nếu text undefined
      };
    };
    //render header table đối tượng
    const renderHeaderTableDoiTuongBaoHiemTaiSan = () => {
      return (
        <div>
          <Form form={formTimKiemPhanTrangDoiTuongBaoHiemTaiSan} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={handleSearchAndPagination}>
            <Row gutter={8} className="items-end">
              {renderFormInputColum({...nd_tim}, 24)}
            </Row>
          </Form>
        </div>
      );
    };

    //renderTable đối tượng chi tiết
    const renderTableDoiTuongCT = () => {
      return (
        <Table<TableDoiTuongCTDataType>
          className="table-doi-tuong-ct no-header-border-radius"
          // className={getNoHoverTableClassName("table-vai-tro no-header-border-radius")}
          {...defaultTableProps}
          style={{cursor: "pointer"}}
          onRow={(record, rowIndex) => {
            return {
              style: {
                cursor: "pointer",
                // background: record.ngay_ad && record.ngay_ad === selectedCauHinhDoiTuongCT ? "#96BF49" : undefined,
              },
            };
          }}
          title={null}
          pagination={false}
          columns={(doiTuongCTColumns || []).map(
            renderColumn,
            //   item => {
            //   // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
            //   return {
            //     ...item,
            //     ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchDoiTuongCTProps(item.key as keyof TableDoiTuongCTDataType, item.title) : {}),
            //   };
            // }
          )}
          // footer={renderTableDoiTuongCTFooter}

          dataSource={dataSource}
          bordered
          scroll={dataSource.length > pageSizeCT ? {y: 165} : undefined}
        />
      );
    };
    // Helper function để render icon theo giá trị sdbs
    const iConSstyle = {marginRight: "8px", fontSize: "15px", fontWeight: "bold"};
    const renderSdbsIcon = (sdbs: string) => {
      switch (sdbs) {
        case "N": // Đối tượng mới
          return <PlusOutlined style={{...iConSstyle, color: "#52c41a"}} title="Đối tượng mới" />;
        case "S": // Đối tượng sửa đổi
          return <WarningOutlined style={{...iConSstyle, color: "orange"}} title="Đối tượng sửa đổi" />;
        case "K": // Đối tượng không sửa đổi
          return <InfoCircleOutlined style={{...iConSstyle, color: "#8c8c8c"}} title="Đối tượng không sửa đổi" />;
        case "H": // Đối tượng kết thúc hiệu lực
          return <CloseOutlined style={{...iConSstyle, color: "#ff4d4f"}} title="Đối tượng kết thúc hiệu lực" />;
        default:
          return null;
      }
    };
    //render bảng danh sách đối tượng XE
    const renderTableDanhSachDoiTuongTaiSan = () => {
      return (
        <div>
          <Table<TableDoiTuongColumnDataType>
            className="custom-table-title-padding mt-5"
            dataSource={dataTableListDoiTuongBaoHiemTaiSan} //mảng dữ liệu record được hiển thị
            columns={
              (tableDoiTuongColumn || []).map(item => {
                // Override render function cho column bien_xe để thêm icon
                if ("dataIndex" in item && item.dataIndex === "ten") {
                  return {
                    ...item,
                    render: (text: any, record: any) => {
                      if (record.key && record.key.toString().includes("empty")) return "\u00A0";

                      const icon = renderSdbsIcon(record.sdbs);
                      const bienXeText = text !== undefined ? text : "";

                      return (
                        <span className="flex items-center">
                          {icon}
                          {bienXeText}
                        </span>
                      );
                    },
                  };
                }
                return {...item};
              }) || []
            } //định nghĩa cột của table
            loading={loading} //hiển thị loading khi đang gọi API để loading data
            sticky
            rowClassName={record => (record.so_id_dt && record.so_id_dt === doiTuongTaiSanSelected?.gcn?.so_id_dt ? "table-row-active" : "")} // ✅ Set class cho dòng được chọn
            pagination={{
              ...defaultPaginationTableProps,
              total: tongSoDongDoiTuongBaoHiemTaiSan,
              pageSize: PAGE_SIZE,
              current: page, //set current page
              onChange: (page, pageSize) => {
                handleSearchAndPagination(undefined, page);
              },
              locale: {
                jump_to: "Tới trang",
                page: "",
              },
            }}
            showHeader={true}
            title={renderHeaderTableDoiTuongBaoHiemTaiSan}
            bordered //set border cả table
            onRow={record => {
              return {
                style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
                onClick: async () => {
                  await handleSelectDoiTuongBaoHiemTaiSan(record);
                },
              };
            }}
            summary={() => (
              <Table.Summary fixed>
                <Table.Summary.Row>
                  {/* Cột đầu tiên: merge các cột trước cột phí bảo hiểm */}
                  <Table.Summary.Cell index={0} colSpan={1} className="!p-[8px]">
                    <div className="text-center font-medium">Tổng phí bảo hiểm</div>
                  </Table.Summary.Cell>
                  {/* Cột phí bảo hiểm: hiển thị tổng */}
                  <Table.Summary.Cell index={1} align="right" className="!p-[8px]">
                    <div className="text-right font-medium">{formatCurrencyUS(tongPhiBaoHiemTaiSanFromAPI)}</div>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            )}
          />
        </div>
      );
    };

    //render form nhập thông tin đối tượng
    const renderForm = () => (
      <Form form={formNhapDoiTuongBaoHiemTaiSan} layout="vertical" className="ml-2 mt-4">
        <Row gutter={16}>
          {renderFormInputColum({
            ...nhom_dt,
            onChange: handleChangeNhomDoiTuong,
            options: listNhomDoiTuong,
          })}
          {renderFormInputColum(
            {
              ...ten,
              // disabled: watchNhomDoiTuong === "NHA_CUA" ? true : false,
              // component: watchNhomDoiTuong === "NHA_CUA" ? "select" : "input",
              // options: [{ma: doiTuongSelected?.ma, ten: doiTuongSelected?.ten}],
              // open: false,
              // dropdownStyle: {display: "none"},
              // labelInValue: true,
              // onClick: () => {
              //   if (watchNhomDoiTuong === "NHA_CUA") {
              //     setCurrentInputField("ten");
              //     refModelDanhSachDoiTuong.current?.open();
              //   }
              // },
            },
            8,
          )}
          {renderFormInputColum({...gcn}, 6)}
          {renderFormInputColum(
            {
              ...dt_ttrr,
              disabled: watchNhomDoiTuong === "NHA_CUA" ? false : true,
              options: [{ma: doiTuongSelected?.ma, ten: doiTuongSelected?.ten}],
              open: false,
              dropdownStyle: {display: "none"},
              // labelInValue: true,
              onClick: () => {
                setCurrentInputField("dt_ttrr");
                refModelDanhSachDoiTuong.current?.open();
              },
            },
            6,
          )}
        </Row>

        <Row gutter={16}>
          {renderFormInputColum({...vi_tri, options: VI_TRI, onChange: handleChangeViTri, disabled: watchDTTTRR ? true : false})}
          {renderFormInputColum({
            ...tinh_thanh,
            options: listTinhThanh,
            onChange: handleChangeTinhThanh,
            disabled: watchViTri === "K" || watchDTTTRR ? true : false,
            rules: watchViTri === "K" ? [] : [ruleRequired],
          })}
          {renderFormInputColum({
            ...phuong_xa,
            options: listPhuongXa,
            // fieldNames: {label: "ten", value: "ma"},
            disabled: watchViTri === "K" || watchDTTTRR ? true : false,
            rules: watchViTri === "K" ? [] : [ruleRequired],
          })}
          {renderFormInputColum({...dchi, disabled: watchViTri === "K" || watchDTTTRR ? true : false, rules: watchViTri === "K" ? [] : [ruleRequired]}, 6)}

          {renderFormInputColum({...latitude, disabled: watchViTri === "K" || watchDTTTRR ? true : false, rules: watchViTri === "K" ? [] : [ruleRequired]}, 3)}
          {renderFormInputColum({...longitude, disabled: watchViTri === "K" || watchDTTTRR ? true : false, rules: watchViTri === "K" ? [] : [ruleRequired]}, 3)}
        </Row>

        <Row gutter={16}>
          {/* {renderFormInputColum({...vip, options: listHDVipSelect})} */}
          {renderFormInputColum({...gia_tri})}
          {renderFormInputColum({
            ...ngay_cap,
            onChange: (date: Dayjs) => {
              formNhapDoiTuongBaoHiemTaiSan.setFields([
                {name: "ngay_hl", value: date},
                {name: "ngay_kt", value: date.add(1, "y")},
              ]);
            },
          })}
          {renderFormInputColum({
            ...gio_hl,
            onChange: (date: Dayjs) => {
              formNhapDoiTuongBaoHiemTaiSan.setFields([{name: "gio_kt", value: date}]);
            },
          })}
          {renderFormInputColum({
            ...ngay_hl,
            onChange: (date: Dayjs) => {
              formNhapDoiTuongBaoHiemTaiSan.setFields([{name: "ngay_kt", value: date.add(1, "y")}]);
            },
          })}
          {renderFormInputColum({...gio_kt})}
          {renderFormInputColum({...ngay_kt})}
          {/* các input ẩn */}
          {renderFormInputColum({...so_id_dt})}
          {renderFormInputColum({...so_id})}
          {renderFormInputColum({...dk})}
          {renderFormInputColum({...dkbs})}
          {/* Đăng ký field ẩn để lưu dữ liệu gcn_ct vào store của Form */}
          {/* <Form.Item name="gcn_ct" hidden /> */}
          <Form.Item name="ttinh" hidden />
        </Row>
      </Form>
    );
    //render tab thông tin đối tượng bảo hiểm
    const renderTabThongTinDoiTuong = () => {
      return (
        <div className="ml-2">
          <Tabs
            animated={true}
            size="small"
            defaultActiveKey="1"
            tabBarStyle={{marginBottom: "8px"}}
            items={[
              {
                key: "1",
                label: "Thông tin đối tượng bảo hiểm",
                children: (
                  <>
                    {renderForm()}
                    {renderTabChiTietThongTinDoiTuong()}
                  </>
                ),
                // children: renderForm(),
              },
              {
                key: "2",
                label: "Danh sách tài sản",
                children: (
                  <RenderTableDanhSachTaiSan
                    columns={tableTaiSanColumn || []}
                    doiTuongTaiSanSelected={doiTuongTaiSanSelected}
                    // onRowClick={handleSelectTaiSan}
                    // pageSize={dynamicPageSize}
                  />
                ),
              },
            ]}
          />
        </div>
      );
    };
    //render tab chi tiết đối tượng thuộc tính
    const renderTabChiTietThongTinDoiTuong = () => {
      return (
        <div className="ml-2">
          <Tabs
            animated={true}
            size="small"
            defaultActiveKey="1"
            tabBarStyle={{marginBottom: "8px"}}
            items={[
              {
                key: "1",
                label: "Thông tin chi tiết",
                children: <>{renderTableDoiTuongCT()}</>,
                // children: renderForm(),
              },
            ]}
          />
        </div>
      );
    };

    return (
      <>
        <Row>
          <Col span={6}>
            {/* Table danh sách đối tượng */}
            {renderTableDanhSachDoiTuongTaiSan()}
            <Row gutter={8} className="mt-5">
              {/* <Col>
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="w-full"></Button>
              </Form.Item>
            </Col> */}
              <Col>
                <Button type="dashed" icon={<UploadOutlined />} className="w-full" onClick={handleImportExcel}>
                  Import
                </Button>
              </Col>
              <Col>
                <Button type="primary" loading={isExportingExcel} icon={<DownloadOutlined />} className="w-full" onClick={handleExportExcel}>
                  Export
                </Button>
              </Col>
              <Col>
                <Button
                  className="w-full"
                  type="primary"
                  icon={<PlusCircleOutlined />}
                  onClick={() => {
                    formNhapDoiTuongBaoHiemTaiSan.resetFields();
                    setDoiTuongTaiSanSelected(null);
                  }}
                  loading={loading}>
                  Thêm mới
                </Button>
              </Col>
            </Row>
          </Col>

          <Col span={18}>{renderTabThongTinDoiTuong()}</Col>
        </Row>

        {/* Modal Import Excel */}
        <ModalImportExcel ref={modalImportExcelRef} onDataConfirm={handleExcelDataConfirm} onCancel={handleExcelImportCancel} />
        <ModalDanhSachDoiTuong
          ref={refModelDanhSachDoiTuong}
          onSelectDoiTuong={data => {
            if (data) {
              setDoiTuongSelected(data);

              formNhapDoiTuongBaoHiemTaiSan.setFieldValue("dt_ttrr", data.ma);
              formNhapDoiTuongBaoHiemTaiSan.setFieldValue("tinh_thanh", data.tinh_thanh);
              formNhapDoiTuongBaoHiemTaiSan.setFieldValue("phuong_xa", data.phuong_xa);
              formNhapDoiTuongBaoHiemTaiSan.setFieldValue("dchi", data.dchi);
              formNhapDoiTuongBaoHiemTaiSan.setFieldValue("latitude", data.latitude);
              formNhapDoiTuongBaoHiemTaiSan.setFieldValue("longitude", data.longitude);
              formNhapDoiTuongBaoHiemTaiSan.setFieldValue("vi_tri", "C");

              // else if (currentInputField === "dt_ttrr") {
              //   formNhapDoiTuongBaoHiemTaiSan.setFieldValue("dt_ttrr", data.ma);
              // }
              // formNhapDoiTuongBaoHiemTaiSan.setFieldValue("dt_ttrr", data.ma);
            } else {
              formNhapDoiTuongBaoHiemTaiSan.setFieldValue("dt_ttrr", null);
            }
          }}
        />
      </>
    );
  },
);

ThongTinDoiTuongBaoHiemTaiSanStepComponent.displayName = "ThongTinDoiTuongBaoHiemTaiSanStepComponent";
export const ThongTinDoiTuongBaoHiemTaiSanStep = memo(ThongTinDoiTuongBaoHiemTaiSanStepComponent, isEqual);
