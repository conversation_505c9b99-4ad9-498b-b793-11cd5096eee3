import {Tabs} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useEffect, useImperativeHandle} from "react";
import {useBaoHiemTaiSanContext} from "../../index.context";
import {RenderTabCauHinhDongBaoHiem} from "./RenderTab_CauHinhDongBaoHiem";
import {RenderTabCauHinhTaiBaoHiem} from "./RenderTab_CauHinhTaiBaoHiem";
import {RenderTabThongTinThanhToan} from "./RenderTab_ThongTinThanhToan";

const ThongTinCauHinhBaoHiemTaiSanStepComponent = forwardRef<any, {tableHeight?: number; disabled?: boolean; pageSize?: number}>(({tableHeight, disabled, pageSize = 20}, ref) => {
  useImperativeHandle(ref, () => ({
    resetForm: () => {},
  }));

  const {loading, layDanhSachCauHinhDongCuaHopDongBaoHiemTaiSan, layDanhSachCauHinhTaiBHCuaHopDongBaoHiemTaiSan, chiTietHopDongBaoHiemTaiSan} = useBaoHiemTaiSanContext();

  useEffect(() => {
    if (chiTietHopDongBaoHiemTaiSan?.so_id) {
      layDanhSachCauHinhDongCuaHopDongBaoHiemTaiSan({so_id: chiTietHopDongBaoHiemTaiSan.so_id});
      layDanhSachCauHinhTaiBHCuaHopDongBaoHiemTaiSan({so_id: chiTietHopDongBaoHiemTaiSan.so_id});
    }
  }, [chiTietHopDongBaoHiemTaiSan]);

  // RENDER
  return (
    <Tabs
      className="mt-3 [&_.ant-tabs-nav]:mb-0"
      animated={false}
      size="small"
      // tabPosition={"left"}
      defaultActiveKey="1"
      // type="card"
      items={[
        {
          key: "1",
          label: "Thông tin thanh toán",
          children: <RenderTabThongTinThanhToan pageSize={pageSize} disabled={disabled} />,
        },
        {
          key: "2",
          label: "Thông tin đồng bảo hiểm",
          children: <RenderTabCauHinhDongBaoHiem pageSize={pageSize} disabled={disabled} />,
        },
        {
          key: "3",
          label: "Thông tin tái bảo hiểm",
          children: <RenderTabCauHinhTaiBaoHiem pageSize={pageSize} disabled={disabled} />,
        },
      ]}
      // onChange={onChange}
    />
  );
});

ThongTinCauHinhBaoHiemTaiSanStepComponent.displayName = "ThongTinCauHinhBaoHiemTaiSanStepComponent";
export const ThongTinCauHinhBaoHiemTaiSanStep = memo(ThongTinCauHinhBaoHiemTaiSanStepComponent, isEqual);
