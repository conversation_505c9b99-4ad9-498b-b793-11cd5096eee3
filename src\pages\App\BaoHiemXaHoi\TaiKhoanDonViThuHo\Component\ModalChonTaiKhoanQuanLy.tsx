import {CheckCircleOutlined, ClearOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Checkbox, Col, Flex, Form, Input, InputRef, Modal, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useQuanLyTaiKhoanDonViThuHoContext} from "../index.context";
import "../index.default.scss";

// Types
export interface IModalChonTaiKhoanQuanLyRef {
  open: (maTaiKhoanCha: string) => void;
  close: () => void;
}

export interface ModalChonTaiKhoanQuanLyProps {
  onSelectTaiKhoanQuanLy: (danhSachMaChon: string[]) => void;
}

export interface TableTaiKhoanQuanLyDataType {
  key: string;
  ma_doi_tac?: string;
  ma_dvi?: string;
  ma?: string;
  ten?: string;
  email?: string;
  dthoai?: string;
  chon?: number; // 0 = chưa chọn, 1 = đã chọn
}

// Columns definition
const taiKhoanQuanLyColumns: TableColumnType<TableTaiKhoanQuanLyDataType>[] = [
  {
    title: "Chọn",
    dataIndex: "chon",
    key: "chon",
    width: 60,
    align: "center",
    render: (chon: number, record) => {
      if (record.key.toString().includes("empty")) return "";
      return (
        <Checkbox 
          checked={chon === 1}
          onChange={() => {}} // Sẽ được handle bởi onRow click
        />
      );
    },
  },
  {title: "Mã", dataIndex: "ma", key: "ma", width: 120, align: "center"},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 200, align: "left"},
  {title: "Email", dataIndex: "email", key: "email", width: 180, align: "left"},
  {title: "Điện thoại", dataIndex: "dthoai", key: "dthoai", width: 120, align: "center"},
];

type DataIndex = keyof TableTaiKhoanQuanLyDataType;

const ModalChonTaiKhoanQuanLyComponent = forwardRef<IModalChonTaiKhoanQuanLyRef, ModalChonTaiKhoanQuanLyProps>(
  ({onSelectTaiKhoanQuanLy}: ModalChonTaiKhoanQuanLyProps, ref) => {
    useImperativeHandle(ref, () => ({
      open: (maTaiKhoanCha: string) => {
        setMaTaiKhoanCha(maTaiKhoanCha);
        setIsOpen(true);
        initData(maTaiKhoanCha);
      },
      close: () => setIsOpen(false),
    }));

    const {loading, layDanhSachTaiKhoanQuanLy, capNhatDanhSachTaiKhoanQuanLy} = useQuanLyTaiKhoanDonViThuHoContext();
    
    const [isOpen, setIsOpen] = useState(false);
    const [maTaiKhoanCha, setMaTaiKhoanCha] = useState<string>("");
    const [danhSachTaiKhoan, setDanhSachTaiKhoan] = useState<TableTaiKhoanQuanLyDataType[]>([]);
    const [danhSachMaChon, setDanhSachMaChon] = useState<string[]>([]);
    const [searchText, setSearchText] = useState("");
    const [searchedColumn, setSearchedColumn] = useState<DataIndex | "">("");
    const refSearchInputTable = useRef<InputRef>(null);

    const initData = async (maTaiKhoanCha: string) => {
      try {
        console.log("[ModalChonTaiKhoanQuanLy] initData với maTaiKhoanCha:", maTaiKhoanCha);

        // Gọi API lấy danh sách tài khoản quản lý
        const response = await layDanhSachTaiKhoanQuanLy(maTaiKhoanCha);
        console.log("[ModalChonTaiKhoanQuanLy] API response:", response);

        if (Array.isArray(response)) {
          const tableData: TableTaiKhoanQuanLyDataType[] = response.map((item: any, index: number) => ({
            key: index.toString(),
            ma_doi_tac: item.ma_doi_tac,
            ma_dvi: item.ma_dvi,
            ma: item.ma,
            ten: item.ten,
            email: item.email,
            dthoai: item.dthoai,
            chon: item.chon || 0, // 0 = chưa chọn, 1 = đã chọn
          }));

          setDanhSachTaiKhoan(tableData);

          // Set danh sách đã chọn từ dữ liệu
          const daChon = tableData.filter(item => item.chon === 1).map(item => item.ma || "");
          setDanhSachMaChon(daChon);

          console.log("[ModalChonTaiKhoanQuanLy] Đã set dữ liệu:", {
            tableData,
            daChon
          });
        } else {
          console.log("[ModalChonTaiKhoanQuanLy] Response không phải array:", response);
          setDanhSachTaiKhoan([]);
          setDanhSachMaChon([]);
        }

      } catch (error) {
        console.log("[ModalChonTaiKhoanQuanLy] initData error:", error);
        setDanhSachTaiKhoan([]);
        setDanhSachMaChon([]);
      }
    };

    const dataTableTaiKhoanQuanLy = useMemo<TableTaiKhoanQuanLyDataType[]>(() => {
      try {
        const tableData = danhSachTaiKhoan.map((item, index) => ({
          ...item,
          key: item.key || index.toString(),
        }));
        
        const arrEmptyRow: TableTaiKhoanQuanLyDataType[] = fillRowTableEmpty(
          tableData.length, 
          defaultPaginationTableProps.defaultPageSize
        );
        return [...tableData, ...arrEmptyRow];
      } catch (error) {
        console.log("dataTableTaiKhoanQuanLy error", error);
        return [];
      }
    }, [danhSachTaiKhoan]);

    const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      confirm();
      setSearchText(selectedKeys[0]);
      setSearchedColumn(dataIndex);
    }, []);

    const handleReset = useCallback(
      (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
        clearFilters();
        setSearchText("");
        handleSearch([""], confirm, dataIndex);
      },
      [handleSearch],
    );

    const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TableTaiKhoanQuanLyDataType> => ({
      filterDropdown: dataIndex !== "chon" ? (filterDropdownParams) => (
        <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
          <Input
            ref={refSearchInputTable}
            placeholder={`Tìm theo ${title}`}
            value={filterDropdownParams.selectedKeys[0]}
            onChange={e => filterDropdownParams.setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => handleSearch(filterDropdownParams.selectedKeys as string[], filterDropdownParams.confirm, dataIndex)}
            style={{display: "block", marginRight: 8}}
          />
          <Tooltip title="Tìm kiếm">
            <Button 
              type="primary" 
              shape="circle" 
              icon={<SearchOutlined />} 
              onClick={() => handleSearch(filterDropdownParams.selectedKeys as string[], filterDropdownParams.confirm, dataIndex)} 
              className="mr-2" 
            />
          </Tooltip>
          <Tooltip title="Xoá">
            <Button 
              type="primary" 
              shape="circle" 
              icon={<ClearOutlined />} 
              onClick={() => filterDropdownParams.clearFilters && handleReset(filterDropdownParams.clearFilters, filterDropdownParams.confirm, dataIndex)} 
            />
          </Tooltip>
        </div>
      ) : undefined,
      
      onFilter: (value, record) => {
        return (
          record[dataIndex]
            ?.toString()
            .toLowerCase()
            .includes((value as string).toLowerCase()) || false
        );
      },
      
      filterDropdownProps: {
        onOpenChange(open) {
          if (open) {
            setTimeout(() => refSearchInputTable.current?.select(), 100);
          }
        },
      },
      
      render: (text, record) => {
        if (dataIndex === "chon") {
          return record.key.toString().includes("empty") ? "" : (
            <Checkbox 
              checked={record.chon === 1}
              onChange={() => {}} // Handle by onRow
            />
          );
        }
        
        return searchedColumn === dataIndex ? (
          <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
        ) : text !== undefined ? (
          text
        ) : (
          <Tag color={"transparent"} className="!text-white text-[11px]">
            {"\u00A0"}
          </Tag>
        );
      },
    });

    const handleRowClick = (record: TableTaiKhoanQuanLyDataType) => {
      if (record.key.toString().includes("empty") || !record.ma) return;
      
      const ma = record.ma;
      const isCurrentlySelected = danhSachMaChon.includes(ma);
      
      let newDanhSachMaChon: string[];
      if (isCurrentlySelected) {
        // Bỏ chọn
        newDanhSachMaChon = danhSachMaChon.filter(item => item !== ma);
      } else {
        // Chọn thêm
        newDanhSachMaChon = [...danhSachMaChon, ma];
      }
      
      setDanhSachMaChon(newDanhSachMaChon);
      
      // Cập nhật state hiển thị
      setDanhSachTaiKhoan(prev => 
        prev.map(item => 
          item.ma === ma 
            ? {...item, chon: isCurrentlySelected ? 0 : 1}
            : item
        )
      );
    };

    const onPressXacNhan = async () => {
      try {
        console.log("[ModalChonTaiKhoanQuanLy] onPressXacNhan với:", {
          maTaiKhoanCha,
          danhSachMaChon
        });

        // Gọi API lưu danh sách tài khoản quản lý
        await capNhatDanhSachTaiKhoanQuanLy(maTaiKhoanCha, danhSachMaChon);

        // Callback để cập nhật UI
        onSelectTaiKhoanQuanLy(danhSachMaChon);
        setIsOpen(false);
      } catch (error) {
        console.log("[ModalChonTaiKhoanQuanLy] onPressXacNhan error:", error);
        // Không đóng modal nếu có lỗi
      }
    };

    const renderFooter = () => {
      return (
        <Form.Item>
          <Button 
            type="default" 
            onClick={() => setIsOpen(false)} 
            className="mr-2"
          >
            Hủy
          </Button>
          <Button 
            type="primary" 
            onClick={onPressXacNhan} 
            icon={<CheckCircleOutlined />}
            iconPosition="end"
          >
            Chọn ({danhSachMaChon.length})
          </Button>
        </Form.Item>
      );
    };

    return (
      <Flex vertical gap="middle" align="flex-start">
        <Modal
          maskClosable={false}
          title="Chọn tài khoản quản lý"
          className="modal-chon-tai-khoan-quan-ly"
          open={isOpen}
          onOk={() => setIsOpen(false)}
          onCancel={() => setIsOpen(false)}
          width={{
            xs: "85%",
            sm: "85%", 
            md: "85%",
            lg: "85%",
            xl: "85%",
            xxl: "85%",
          }}
          style={{top: 10}}
          footer={renderFooter()}
        >
          <Table<TableTaiKhoanQuanLyDataType>
            {...defaultTableProps}
            className="tai-khoan-quan-ly"
            loading={loading}
            onRow={record => ({
              style: {cursor: loading ? "progress" : "pointer"},
              onClick: () => handleRowClick(record),
            })}
            columns={taiKhoanQuanLyColumns.map(item => ({
              ...item,
              ...(item.key && typeof item.title === "string" && item.key !== "chon" 
                ? getColumnSearchProps(item.key as keyof TableTaiKhoanQuanLyDataType, item.title) 
                : {}),
            }))}
            dataSource={dataTableTaiKhoanQuanLy}
            pagination={{
              ...defaultPaginationTableProps,
              total: danhSachTaiKhoan.length,
              showSizeChanger: false,
            }}
          />
        </Modal>
      </Flex>
    );
  }
);

ModalChonTaiKhoanQuanLyComponent.displayName = "ModalChonTaiKhoanQuanLyComponent";
export const ModalChonTaiKhoanQuanLy = memo(ModalChonTaiKhoanQuanLyComponent, isEqual);
