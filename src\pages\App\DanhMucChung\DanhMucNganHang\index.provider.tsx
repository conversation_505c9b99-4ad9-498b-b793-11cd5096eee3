import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import React, {PropsWithChildren, useEffect, useMemo, useCallback} from "react";
import {useState} from "react";
import {DanhMucNganHangContextProps} from "./index.model";
import {DanhMucNganHangContext} from "./index.context";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
// import {defaultFormValue} from "./index.configs";
import {message} from "antd";

const DanhMucNganHangProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  console.log("Danh mục ngân hàng PROVIDER", children);
  const mutateUseCommonExecute = useCommonExecute();
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [danhSachNganHangPhanTrang, setDanhSachNganHang] = useState<Array<CommonExecute.Execute.IDanhMucNganHang>>([]);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucNganHangParams & ReactQuery.IPhanTrang>({
    ma: "",
    ten: "",
    trang_thai: "",
    //  trang: 1,
    //  so_dong: 13,
    actionCode: "",
  });
  useEffect(() => {
    initData();
  }, []);
  const initData = () => {
    layDanhSachNganHangPhanTrang(filterParams);
    getListDoiTac();
  };
  useEffect(() => {
    layDanhSachNganHangPhanTrang(filterParams);
  }, [filterParams]);
  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);

  const layDanhSachNganHangPhanTrang = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangDanhMucNganHangParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_NGAN_HANG,
        };

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);

        const data = response.data.data;
        console.log("data", data);
        setDanhSachNganHang(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("Lấy danh sách ngân hàng error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy chi tiết 1 ngân hàng
  const layChiTietNganHang = useCallback(
    async (item: ReactQuery.IChiTietDanhMucNganHangParams): Promise<CommonExecute.Execute.IDanhMucNganHang | null> => {
      try {
        const params = {
          ma: item.ma,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_NGAN_HANG,
        };
        console.log("params", params);
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        console.log("responseData", responseData);
        return responseData.data as CommonExecute.Execute.IDanhMucNganHang;
      } catch (error: any) {
        console.log("layChiTietNganHang error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  const onUpdateDanhMucNganHang = useCallback(
    async (body: ReactQuery.ICapNhatDanhMucNganHangParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_DANH_MUC_NGAN_HANG,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          initData();
          return responseData.data as unknown as number;
        }
      } catch (error: any) {
        console.log("onUpdateDanhMucNganHang error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<DanhMucNganHangContextProps>(
    () => ({
      loading: mutateUseCommonExecute.isLoading,
      tongSoDong,
      // defaultFormValue,
      layDanhSachNganHangPhanTrang,
      filterParams,
      danhSachNganHangPhanTrang,
      listDoiTac,
      setFilterParams,
      layChiTietNganHang,
      onUpdateDanhMucNganHang,
      getListDoiTac,
    }),
    [danhSachNganHangPhanTrang, tongSoDong, mutateUseCommonExecute, layDanhSachNganHangPhanTrang, layChiTietNganHang, onUpdateDanhMucNganHang, listDoiTac, getListDoiTac],
  );

  return <DanhMucNganHangContext.Provider value={value}>{children}</DanhMucNganHangContext.Provider>;
};
export default DanhMucNganHangProvider;
