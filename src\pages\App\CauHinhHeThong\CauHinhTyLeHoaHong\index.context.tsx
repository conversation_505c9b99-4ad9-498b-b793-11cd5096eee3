import {createContext, useContext} from "react";
import {CauHinhTyLeHoaHongContextProps} from "./index.model";
//createContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const CauHinhTyLeHoaHongContext = createContext<CauHinhTyLeHoaHongContextProps>({
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  danhSachDaiLy: [],
  listDoiTac: [],
  tongSoDong: 0,
  filterParams: {},
  danhSachTyLeHoaHong: [],
  cayTyLeHoaHong: [],
  setCayTyLeHoaHong: () => {},
  layCayTyLeHoaHong: () => Promise.resolve([]),
  onUpdateCauHinhTyLeHoaHong: () => {},
  layDanhSachTyLeHoaHong: () => {},
  setDanhSachTyLeHoaHong: () => {},
  getListDoiTac: () => Promise.resolve(),
  loading: false,
  layDanhSachDaiLyPhanTrang: () => Promise.resolve({data: [], tong_so_dong: 0}),
  layChiTietDaiLy: params => Promise.resolve(null),
  setFilterParams: () => {},
});

//useContext(someContext): là React Hook cho phép bạn đọc và subscribe context từ component của mình.
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useCauHinhTyLeHoaHongContext = () => useContext(CauHinhTyLeHoaHongContext);
