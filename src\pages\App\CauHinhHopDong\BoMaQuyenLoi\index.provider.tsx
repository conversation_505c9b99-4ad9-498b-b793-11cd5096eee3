import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {BoMaQuyenLoiContextProps} from "./index.model";
import {BoMaQuyenLoiContext} from "./index.context";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";

import {message} from "antd";
const BoMaQuyenLoiProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [danhSachBoMaQuyenLoi, setDanhSachBoMaQuyenLoi] = useState<Array<CommonExecute.Execute.IDanhSachBoMaQuyenLoi>>([]);
  const [danhSachBoMaQuyen<PERSON>oi<PERSON>ha, setDanhSachBoMaQuyenLoiCha] = useState<Array<CommonExecute.Execute.IDanhSachBoMaQuyenLoi>>([]);
  const [listNghiepVu, setListNghiepVu] = useState<Array<CommonExecute.Execute.IDanhMucNghiepVu>>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [tongSoDongQLCha, setTongSoDongQLCha] = useState<number>(0);
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [chiTietBoMaQuyenLoi, setChiTietBoMaQuyenLoi] = useState<CommonExecute.Execute.IChiTietBoMaQuyenLoi>({});
  const [listSanPham, setListSanPham] = useState<Array<CommonExecute.Execute.IDanhMucSanPham>>([]);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams & ReactQuery.IPhanTrang>({
    ma_doi_tac_ql: "",
    nv: "",
    ma_sp: "",
    nd_tim: "",
    trang_thai: "",
    trang: 1,
    so_dong: 13,
    actionCode: "",
  });
  useEffect(() => {
    initData();
  }, []);
  const initData = () => {
    layDanhSachBoMaQuyenLoi(filterParams);
    getListDoiTac();
    getListNghiepVu();
  };
  useEffect(() => {
    layDanhSachBoMaQuyenLoi(filterParams);
    console.log("params quyen lợi provider efect", filterParams);
  }, [filterParams]);
  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);
  console.log("params quyen lợi provider", filterParams);
  //nghiệp vụ
  const getListNghiepVu = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_NGHIEP_VU,
      });
      setListNghiepVu(response?.data as Array<CommonExecute.Execute.IDanhMucNghiepVu>);
    } catch (error) {
      console.log("getListNghiepVu error ", error);
    }
  }, [mutateUseCommonExecute]);

  const layChiTietBoMaQuyenLoi = useCallback(
    async (body: ReactQuery.IChiTietBoMaQuyenLoiParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CHI_TIET_BO_MA_QUYEN_LOI,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        setChiTietBoMaQuyenLoi(response.data);
        console.log("data chi tiết", response.data);
        return response.data as CommonExecute.Execute.IChiTietBoMaQuyenLoi;
      } catch (error: any) {
        console.log("layChiTietBoMaQuyenLoi error ", error.message | error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  const layDanhSachBoMaQuyenLoi = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_BO_MA_QUYEN_LOI,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data;
        console.log("data", data);
        setDanhSachBoMaQuyenLoi(data);
        setTongSoDong(response.data.tong_so_dong);
        console.log("Gọi danh sách bộ mã quyền lợi");
      } catch (error: any) {
        console.log("layDanhSachBoMaQuyenLoi error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  const layDanhSachBoMaQuyenLoiCha = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_BO_MA_QUYEN_LOI,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data;
        console.log("danh sách quyền lợi cha", data);
        setDanhSachBoMaQuyenLoiCha(data);
        setTongSoDongQLCha(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("layDanhSachBoMaQuyenLoiCha error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );

  const getListSanPham = useCallback(
    async (params: ReactQuery.ILietKeSanPhamParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          trang: 1,
          so_dong: 1000,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DS_SAN_PHAM,
        });
        console.log("Danh sách sản phẩm ", response);
        return response?.data || {data: [], tong_so_dong: 0};
      } catch (error) {
        console.log("getListsanpham error ", error);
        return {data: [], tong_so_dong: 0};
      }
    },
    [mutateUseCommonExecute],
  );

  const getListSanPhamTheoDoiTac = useCallback(
    async (params: ReactQuery.ILietKeSanPhamParams) => {
      try {
        const response = await getListSanPham(params);
        const updatedData = [...response.data]; // Sao chép mảng để tránh mutate trực tiếp
        setListSanPham(updatedData); // Cập nhật state nếu cần
        return {data: updatedData, tong_so_dong: response.tong_so_dong};
      } catch (error) {
        console.log("error", error);
        return {data: [], tong_so_dong: 0}; // Trả về giá trị mặc định khi lỗi
      }
    },
    [getListSanPham],
  );

  const updateBoMaQuyenLoi = useCallback(
    async (body: ReactQuery.IUpdateBoMaQuyenLoiParams) => {
      // try {
      const params = {
        ...body,
        actionCode: ACTION_CODE.CAP_NHAT_BO_MA_QUYEN_LOI,
      };
      const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
      console.log("respon", response);
      if (response && (response.data as unknown as number) === -1) {
        message.success("Cập nhật thông tin thành công!");
        initData();
        return response.data as unknown as number;
      }
      // } catch (error: any) {
      //   console.log("updateBoMaQuyenLoi error ", error.message | error);
      //   return false;
      // }
    },
    [mutateUseCommonExecute],
  );
  const value = useMemo<BoMaQuyenLoiContextProps>(
    () => ({
      danhSachBoMaQuyenLoi,
      loading,
      danhSachBoMaQuyenLoiCha,
      listSanPham,
      listDoiTac,
      tongSoDong,
      tongSoDongQLCha,
      filterParams,
      listNghiepVu,
      layDanhSachBoMaQuyenLoi,
      layDanhSachBoMaQuyenLoiCha,
      layChiTietBoMaQuyenLoi,
      updateBoMaQuyenLoi,
      getListSanPhamTheoDoiTac,
      getListDoiTac,
      setFilterParams,
      getListNghiepVu,
    }),
    [
      filterParams,
      danhSachBoMaQuyenLoi,
      loading,
      tongSoDongQLCha,
      listSanPham,
      danhSachBoMaQuyenLoiCha,
      tongSoDong,
      listDoiTac,
      listNghiepVu,
      setFilterParams,
      getListSanPhamTheoDoiTac,
      layDanhSachBoMaQuyenLoi,
      layDanhSachBoMaQuyenLoiCha,
      layChiTietBoMaQuyenLoi,
      updateBoMaQuyenLoi,
      getListDoiTac,
      getListNghiepVu,
    ],
  );
  return <BoMaQuyenLoiContext.Provider value={value}>{children}</BoMaQuyenLoiContext.Provider>;
};
export default BoMaQuyenLoiProvider;
