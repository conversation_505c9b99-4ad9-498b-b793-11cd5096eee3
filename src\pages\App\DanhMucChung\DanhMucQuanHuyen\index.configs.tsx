import React from "react";
import {TableColumnsType, TableProps} from "antd";

import {IFormInput, ReactQuery} from "@src/@types";
import {REGUlAR_EXPRESSION} from "@src/constants";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage, validationRules} from "@src/hooks";

//Tác dụng: Convert ngày áp dụng từ số hoặc string sang text hiển thị
// DEPRECATED: Logic này đã được move vào content để tương thích với search
// const convertNgayApDungToText = (ngayAd: number | string | null | undefined): string => {
//   //Kiểm tra null, undefined hoặc empty string
//   if (ngayAd === null || ngayAd === undefined || ngayAd === '') {
//     return ''; //Hoặc trả về "-" nếu muốn hiển thị dấu gạch
//   }
//   const ngayAdStr = String(ngayAd);
//   //X<PERSON> lý format number (19000101, 20250701)
//   if (ngayAdStr === '19000101') {
//     return 'Áp dụng từ ngày 01/01/1900';
//   } else if (ngayAdStr === '20250701') {
//     return 'Áp dụng từ ngày 01/07/2025';
//   }
//   
//   //Xử lý format string ("01/01/1900", "01/07/2025")  
//   if (ngayAdStr === '01/01/1900') {
//     return 'Áp dụng từ ngày 01/01/1900';
//   } else if (ngayAdStr === '01/07/2025') {
//     return 'Áp dụng từ ngày 01/07/2025';
//   }
//   //Nếu không match với các case trên, hiển thị ngày gốc hoặc empty
//   if (ngayAdStr === 'null' || ngayAdStr === 'undefined') {
//     return '';
//   }
//   
//   return ngayAdStr;
// };

//Check empty value
const isEmptyValue = (value: any): boolean => {
  return value === null || value === undefined || value === '' || (typeof value === 'string' && value.trim() === '');
};

//===== FORM TÌM KIẾM =====
export interface IFormTimKiemQuanHuyenFieldsConfig {
  ma_tinh: IFormInput;
  ngay_ad: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  trang_thai: IFormInput;
}

//Cấu hình form tìm kiếm quận huyện ở header table
export const FormTimKiemDanhMucQuanHuyen: IFormTimKiemQuanHuyenFieldsConfig = {
  ma_tinh: {
    component: "select",
    name: "ma_tinh",
    label: "Tỉnh/Thành phố",
    placeholder: "Chọn tỉnh/thành phố",
    className: "!mb-0",
  },
  ngay_ad: {
    component: "select",
    name: "ngay_ad",
    label: "Ngày áp dụng",
    placeholder: "Chọn ngày áp dụng",
    className: "!mb-0",
  },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã quận/huyện",
    placeholder: "Nhập mã quận/huyện",
    className: "!mb-0",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên quận/huyện",
    placeholder: "Nhập tên quận/huyện",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};

//===== FORM MODAL CHI TIẾT (TẠO MỚI/SỬA) =====
export interface IFormTaoMoiQuanHuyenFieldsConfig {
  ma_tinh: IFormInput;
  ngay_ad: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  postcode: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
}

//Cấu hình form tạo mới/chỉnh sửa quận huyện trong modal
export const FormTaoMoiQuanHuyen: IFormTaoMoiQuanHuyenFieldsConfig = {
  ma_tinh: {
    component: "select",
    label: "Tỉnh/Thành phố",
    name: "ma_tinh",
    placeholder: "Chọn tỉnh/thành phố",
    rules: [ruleInputMessage.required],
  },
  ngay_ad: {
    component: "select",
    label: "Ngày áp dụng",
    name: "ngay_ad",
    placeholder: "Chọn ngày áp dụng",
    rules: [ruleInputMessage.required],
  },
  ma: {
    component: "input",
    label: "Mã quận/huyện",
    name: "ma",
    placeholder: "Nhập mã quận/huyện",
    rules: [ruleInputMessage.required],
  },
  ten: {
    component: "input",
    label: "Tên quận/huyện",
    name: "ten",
    placeholder: "Nhập tên quận/huyện",
    rules: [ruleInputMessage.required],
  },
  postcode: {
    component: "input",
    label: "Mã bưu điện",
    name: "postcode",
    placeholder: "Nhập mã bưu điện",
    rules: [],
  },
  stt: {
    component: "input",
    label: "Số thứ tự",
    name: "stt",
    placeholder: "Nhập số thứ tự",
    rules: [],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required],
  },
};

//===== FORM MODAL CHI TIẾT (LEGACY) =====
export const FormModalChiTietQuanHuyen: Record<string, IFormInput> = {
  ma_tinh: {
    name: "ma_tinh",
    label: "Tỉnh/Thành phố",
    required: true,
    placeholder: "Chọn tỉnh/thành phố",
    component: "select",
    options: [], //Sẽ được cung cấp từ component
  },
  ma: {
    name: "ma",
    label: "Mã quận/huyện",
    required: true,
    placeholder: "Nhập mã quận/huyện",
    component: "input",
  },
  ten: {
    name: "ten",
    label: "Tên quận/huyện",
    required: true,
    placeholder: "Nhập tên quận/huyện",
    component: "input",
  },
  trang_thai: {
    name: "trang_thai",
    label: "Trạng thái",
    required: true,
    placeholder: "Chọn trạng thái",
    component: "select",
    options: [], //Sẽ được cung cấp từ component
  },
};

//===== RADIO ITEMS - TRẠNG THÁI =====
//Options cho dropdown trạng thái trong form tìm kiếm
export const radioItemTrangThaiQuanHuyenSelect: Array<{ma: string; ten: string}> = [
  {ma: "", ten: "Tất cả"},
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];

export const radioItemTrangThaiQuanHuyenTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

//===== TRẠNG THÁI TẠO MỚI =====
export const TRANG_THAI_TAO_MOI_QUAN_HUYEN = [
  {value: "D", label: "Đang sử dụng"},
  {value: "K", label: "Ngưng sử dụng"},
];

//===== NGÀY ÁP DỤNG =====
export const radioItemNgayApDungQuanHuyenSelect: Array<{ma: string; ten: string}> = [
  {ma: "", ten: "Tất cả"},
  {ma: "19000101", ten: "01/01/1900"},
  {ma: "20250701", ten: "01/07/2025"},
];

export const NGAY_AD_TAO_MOI_QUAN_HUYEN = [
  {ten: "01/01/1900", ma: "19000101"},
  {ten: "01/07/2025", ma: "20250701"},
];

//===== KIỂU DỮ LIỆU TABLE =====
export interface TableQuanHuyenColumnDataType {
  key: string;
  sott?: number;
  ngay_ad?: number;
  ma?: string;
  ten?: string;
  ma_tinh?: string;
  ten_tinh?: string;
  postcode?: string;
  stt?: number;
  trang_thai?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

//===== CỘT TABLE =====
//Cấu hình các cột hiển thị trong bảng danh sách quận huyện
export const tableQuanHuyenColumn: TableProps<TableQuanHuyenColumnDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    align: "center",
    width: 30,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày áp dụng",
    dataIndex: "ngay_ad",
    key: "ngay_ad",
    align: "center",
    width: 80,
    // DEPRECATED: Render function đã được move vào content để tương thích với search
    // render: (ngay_ad: number, record: TableQuanHuyenColumnDataType) => {
    //   //Bỏ qua empty rows
    //   if (record.key?.toString().includes("empty")) {
    //     return "";
    //   }
    //   
    //   return convertNgayApDungToText(ngay_ad);
    // },
    ...defaultTableColumnsProps,
  },
  // {
  //   title: "Mã tỉnh/thành phố",
  //   dataIndex: "ma_tinh",
  //   key: "ma_tinh",
  //   align: "center",
  //   width: 50,
  //   ...defaultTableColumnsProps,
  // },
  {
    title: "Mã quận/huyện",
    dataIndex: "ma",
    key: "ma",
    align: "center",
    width: 80,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên quận/huyện",
    dataIndex: "ten",
    key: "ten",
    width: 80,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã bưu điện",
    dataIndex: "postcode",
    key: "postcode",
    align: "center",
    width: 60,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên tỉnh/thành phố",
    dataIndex: "ten_tinh",
    key: "ten_tinh",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: 100,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: 80,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: 100,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: 80,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 80,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

//keyof: return ra key của interface TableQuanHuyenColumnDataType;
export type TableQuanHuyenColumnDataIndex = keyof TableQuanHuyenColumnDataType;
