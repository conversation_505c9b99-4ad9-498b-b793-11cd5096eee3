import {createContext, useContext} from "react";
import {NhomDoiTuongContextProps} from "./index.model";
//createContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const NhomDoiTuongContext = createContext<NhomDoiTuongContextProps>({
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  danhSachNhomDoiTuong: [],
  listDoiTac: [],
  chiTietNhomDoiTuong: null,
  // listNhomDoiTuongCT: [],
  listNhomDoiTuongCT: [],
  loading: false,
  tongSoDong: 0,
  filterParams: {},
  onDeleteNhomDoiTuongCT: () => Promise.resolve(null),
  getListDoiTac: () => Promise.resolve(),
  layDanhSachNhomDoiTuongPhanTrang: () => Promise.resolve({data: [], tong_so_dong: 0}),
  layChiTietNhomDoiTuong: params => Promise.resolve(null),
  setFilterParams: () => {},
  onUpdateNhomDoiTuong: () => Promise.resolve(null),
});

//useContext(someContext): là React Hook cho phép bạn đọc và subscribe context từ component của mình.
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useNhomDoiTuongContext = () => useContext(NhomDoiTuongContext);
