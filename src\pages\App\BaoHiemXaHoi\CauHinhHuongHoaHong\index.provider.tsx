import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";

import {CauHinhHuongHoaHongContext} from "./index.context";
import {ICauHinhHuongHoaHongContextProps} from "./index.model";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
import {TableTaiKhoanDonViThuHoColumnDataType} from "./index.configs";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";

const CauHinhHuongHoaHongProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();

  const [listTaiKhoanDonViThuHo, setListTaiKhoanDonViThuHo] = useState<Array<CommonExecute.Execute.ITaiKhoanDonViThuHo>>([]);
  const [listDonVi, setListDonVi] = useState<Array<CommonExecute.Execute.IDanhSachDonViBHXH>>([]);
  const [listLoaiHoGiaDinh, setListLoaiHoGiaDinh] = useState<Array<CommonExecute.Execute.ILoaiHoGiaDinh>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [tongSoDongCT, setTongSoDongCT] = useState<number>(0);
  const [chiTietTaiKhoanDonViThuHo, setChiTietTaiKhoanDonViThuHo] = useState<CommonExecute.Execute.ITaiKhoanDonViThuHo>({} as CommonExecute.Execute.ITaiKhoanDonViThuHo);
  const [danhSachCauHinhHuongHoaHongNgayApDung, setDanhSachCauHinhHuongHoaHongNgayApDung] = useState<Array<CommonExecute.Execute.ICauHinhHuongHoaHongNgayApDung>>([]);
  const [chiTietCauHinhHuongHoaHong, setChiTietCauHinhHuongHoaHong] = useState<CommonExecute.Execute.ICauHinhHuongHoaHong>({} as CommonExecute.Execute.ICauHinhHuongHoaHong);
  const [danhSachCauHinhHuongHoaHong, setDanhSachCauHinhHuongHoaHong] = useState<Array<CommonExecute.Execute.ICauHinhHuongHoaHong>>([]);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangTaiKhoanDonViThuHoParams & ReactQuery.IPhanTrang>({ma: "", ten: "", trang_thai: "", trang: 1, so_dong: 20});

  //khởi tạo dữ liệu ban đàu
  useEffect(() => {
    //nè
    initData();
  }, []);
  useEffect(() => {
    getListTaiKhoanDonViThuHo();
    getListLoaiHoGiaDinh();
  }, [filterParams]);

  //Tác dụng: Lấy danh sách đơn vị để hiển thị trong dropdown filter
  const getListDonVi = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ma: "",
        ten: "",
        trang_thai: "",
        trang: 1,
        so_dong: 100,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DON_VI_THU_HO_BHXH,
      } as any);

      if (response.data) {
        console.log("getListDonVi response.data:", response.data);
        //Xử lý response structure để đảm bảo tương thích
        if (Array.isArray(response.data)) {
          setListDonVi(response.data);
        } else if (response.data && typeof response.data === "object") {
          const responseData = response.data as any;
          if (responseData.data && Array.isArray(responseData.data)) {
            setListDonVi(responseData.data);
          } else {
            setListDonVi([]);
          }
        }
      } else {
        console.log("response.data is null/undefined");
      }
    } catch (error) {
      console.log("[Provider] getListDonVi error:", error);
      setListDonVi([]);
    }
  }, [mutateUseCommonExecute]);

  const getChiTietTaiKhoanDonViThuHo = useCallback(
    async (data: TableTaiKhoanDonViThuHoColumnDataType) => {
      try {
        if (!data.ma) {
          return {} as CommonExecute.Execute.ITaiKhoanDonViThuHo;
        }
        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_TAI_KHOAN_DON_VI_THU_HO_BHXH,
        } as any);
        // Xử lý structure response.data.lke
        const responseData = response.data as any; // Type casting để truy cập lke
        if (responseData && responseData.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          const result = responseData.lke[0] as CommonExecute.Execute.ITaiKhoanDonViThuHo;
          setChiTietTaiKhoanDonViThuHo(result);
          return result;
        } else {
          return {} as CommonExecute.Execute.ITaiKhoanDonViThuHo;
        }
      } catch (error) {
        console.log("[Provider] getChiTietTaiKhoanDonViThuHo error:", error);
        return {} as CommonExecute.Execute.ITaiKhoanDonViThuHo;
      }
    },
    [mutateUseCommonExecute],
  );

  //danh sách loại hộ gia đình
  const getListLoaiHoGiaDinh = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_LOAI_HO_GIA_DINH_BHXH,
      } as any);
      if (response.data) {
        setListLoaiHoGiaDinh(response.data);
      }
    } catch (error) {
      console.log("getListLoaiHoGiaDinh error ", error);
    }
  }, [mutateUseCommonExecute]);
  const getListTaiKhoanDonViThuHo = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_TAI_KHOAN_DON_VI_THU_HO_BHXH,
      } as any);
      if (response.data) {
        setListTaiKhoanDonViThuHo(response.data.data);
        setTongSoDong(response.data.tong_so_dong);
      }
    } catch (error) {
      console.log("getListTaiKhoanDonViThuHo error ", error);
    }
  }, [mutateUseCommonExecute]);
  const layDanhSachCauHinhHuongHoaHongNgayApDung = useCallback(
    async (params: ReactQuery.ILietKeCauHinhHuongHoaHongNgayApDungParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LIET_KE_CAU_HINH_HUONG_HOA_HONG_NGAY_AD,
        });

        setDanhSachCauHinhHuongHoaHongNgayApDung(response.data);
        return response.data as CommonExecute.Execute.ICauHinhHuongHoaHongNgayApDung;
      } catch (error) {
        console.log("layDanhSachNganSachNgayApDung error", error);
        return {} as CommonExecute.Execute.ICauHinhHuongHoaHongNgayApDung;
      }
    },
    [mutateUseCommonExecute],
  );
  //cập nhật ngân sách hỗ trợ ngày áp dụng
  const updateCauHinhHuongHoaHongNgayApDung = useCallback(
    async (body: ReactQuery.IUpdateCauHinhHuongHoaHongNgayApDungParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_CAU_HINH_HUONG_HOA_HONG_NGAY_AD,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          return true;
        } else return false;
      } catch (error: any) {
        console.log("updateCauHinhHuongHoaHongNgayApDung error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );
  //xóa ngày áp dụng ngân sách hỗ trợ
  const xoaNgayApDungCauHinhHuongHoaHong = useCallback(
    async (body: ReactQuery.IUpdateCauHinhHuongHoaHongNgayApDungParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.XOA_CAU_HINH_HUONG_HOA_HONG_NGAY_AD,
        };

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Xóa ngày áp dụng thành công!");
          return true;
        } else return false;
      } catch (error: any) {
        console.log("xoaNgayApDungCauHinhHuongHoaHong error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );
  const layDanhSachCauHinhHuongHoaHong = useCallback(
    async (params: ReactQuery.ITimKiemPhanTrangCauHinhHuongHoaHongParams & ReactQuery.IPhanTrang) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_CAU_HINH_HUONG_HOA_HONG,
        } as any);

        if (response.data) {
          setDanhSachCauHinhHuongHoaHong(response.data.data);
          setTongSoDongCT(response.data.tong_so_dong);
        }
      } catch (error) {
        console.log("layDanhSachCauHinhHuongHoaHong error ", error);
      }
    },
    [mutateUseCommonExecute],
  );
  const capNhatCauHinhHuongHoaHong = useCallback(
    async (body: ReactQuery.IUpdateCauHinhHuongHoaHongParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_CAU_HINH_HUONG_HOA_HONG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          return true;
        } else return false;
      } catch (error: any) {
        console.log("capNhatCauHinhHuongHoaHong error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );
  //chi tiết cấu hình hưởng hoa hồng
  const layChiTietCauHinhHuongHoaHong = useCallback(
    async (params: ReactQuery.IChiTietCauHinhHuongHoaHongParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.CHI_TIET_CAU_HINH_HUONG_HOA_HONG,
        });
        // Xử lý structure response.data.lke
        const responseData = response.data as any; // Type casting để truy cập lke
        if (responseData && responseData.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          const result = responseData.lke[0] as CommonExecute.Execute.ICauHinhHuongHoaHong;
          setChiTietCauHinhHuongHoaHong(result);
          return result;
        } else {
          console.log("Không tìm thấy data trong response.data.lke");
          return {} as CommonExecute.Execute.ICauHinhHuongHoaHong;
        }
      } catch (error) {
        console.log("layChiTietCauHinhHuongHoaHong error", error);
        return {} as CommonExecute.Execute.ICauHinhHuongHoaHong;
      }
    },
    [mutateUseCommonExecute],
  );
  //xóa cấu hình hưởng hoa hồng
  const xoaCauHinhHuongHoaHong = useCallback(
    async (body: ReactQuery.IUpdateCauHinhHuongHoaHongParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.XOA_CAU_HINH_HUONG_HOA_HONG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Xóa cấu hình hưởng hoa hồng thành công!");
          return true;
        } else return false;
      } catch (error: any) {
        console.log("xoaCauHinhHuongHoaHong error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );
  //initData chỉ dùng trong useEffect 1 lần, không truyền đi đâu cả, nên không cần memo hoá
  const initData = () => {
    getListDonVi();
  };
  const value = useMemo<ICauHinhHuongHoaHongContextProps>(
    () => ({
      listTaiKhoanDonViThuHo,
      listDonVi,
      listLoaiHoGiaDinh,
      tongSoDong,
      tongSoDongCT,
      loading: mutateUseCommonExecute.isLoading,
      filterParams,
      chiTietTaiKhoanDonViThuHo,
      danhSachCauHinhHuongHoaHongNgayApDung,
      danhSachCauHinhHuongHoaHong,
      chiTietCauHinhHuongHoaHong,
      xoaCauHinhHuongHoaHong,
      layChiTietCauHinhHuongHoaHong,
      layDanhSachCauHinhHuongHoaHong,
      layDanhSachCauHinhHuongHoaHongNgayApDung,
      getListTaiKhoanDonViThuHo,
      getChiTietTaiKhoanDonViThuHo,
      updateCauHinhHuongHoaHongNgayApDung,
      xoaNgayApDungCauHinhHuongHoaHong,
      setFilterParams,
      capNhatCauHinhHuongHoaHong,
    }),
    [
      listTaiKhoanDonViThuHo,
      listDonVi,
      listLoaiHoGiaDinh,
      tongSoDong,
      tongSoDongCT,
      mutateUseCommonExecute,
      filterParams,
      chiTietTaiKhoanDonViThuHo,
      danhSachCauHinhHuongHoaHongNgayApDung,
      danhSachCauHinhHuongHoaHong,
      chiTietCauHinhHuongHoaHong,
      xoaCauHinhHuongHoaHong,
      layChiTietCauHinhHuongHoaHong,
      layDanhSachCauHinhHuongHoaHong,
      layDanhSachCauHinhHuongHoaHongNgayApDung,
      getListTaiKhoanDonViThuHo,
      getChiTietTaiKhoanDonViThuHo,
      updateCauHinhHuongHoaHongNgayApDung,
      xoaNgayApDungCauHinhHuongHoaHong,
      setFilterParams,
      capNhatCauHinhHuongHoaHong,
    ],
  );

  return <CauHinhHuongHoaHongContext.Provider value={value}>{children}</CauHinhHuongHoaHongContext.Provider>;
};

export default CauHinhHuongHoaHongProvider;
