import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useRef, useState} from "react";
import {useDanhSachDonViBHXHContext} from "../index.context";
import {FormChiTietDonViBHXH, IModalThemDonViChaRef, IDonViBHXHSelected, ChiTietDonViBHXHProps, IModalChiTietDanhSachDonViBHXHRef} from "./index.configs";
import {TRANG_THAI_CHI_TIET_DAI_LY} from "../index.configs";
import "../index.default.scss";
import {ModalThemDonViCha} from "./ModalThemDonViCha";

const {ma, ten, dchi, dthoai, logo, mst, ten_e, ten_tat, stt, trang_thai, trang_thai_ten, ma_ct} = FormChiTietDonViBHXH;

const ModalChiTietDonViBHXHComponent = forwardRef<IModalChiTietDanhSachDonViBHXHRef, ChiTietDonViBHXHProps>(({listDoiTac, danhSachDonViBHXH}: ChiTietDonViBHXHProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDonVi?: CommonExecute.Execute.IDanhSachDonViBHXH) => {
      setIsOpen(true);
      if (dataDonVi) setDisableSubmit(true);
      console.log("chitietDaiLy chi tiết", dataDonVi);
      if (dataDonVi) setchitietDonViBHXH(dataDonVi);
    },

    close: () => setIsOpen(false),
  }));

  const [chitietDonViBHXH, setchitietDonViBHXH] = useState<CommonExecute.Execute.IDanhSachDonViBHXH | null>(null);
  const refModelTimDaiLyCha = useRef<IModalThemDonViChaRef>(null);
  const [isOpen, setIsOpen] = useState(false);
  const {loading, onUpdateDanhSachDonViBHXH, filterParams, setFilterParams, layDanhSachDonViBHXH} = useDanhSachDonViBHXHContext();
  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  const [donViBHXHSelected, setDonViBHXHSelected] = useState<IDonViBHXHSelected | null>(null);

  // init form data
  useEffect(() => {
    if (chitietDonViBHXH) {
      const arrFormData = [];
      for (const key in chitietDonViBHXH) {
        arrFormData.push({
          name: key,
          value: chitietDonViBHXH[key as keyof CommonExecute.Execute.IDanhSachDonViBHXH],
        });
      }
      // setchitietDonViBHXH(chitietDonViBHXH);
      const daiLyCha = danhSachDonViBHXH.find(daiLy => daiLy.ma === chitietDonViBHXH.ma_ct);
      if (daiLyCha) {
        setDonViBHXHSelected({
          ma: daiLyCha.ma,
          ten: daiLyCha.ten,
        });
      } else {
        setDonViBHXHSelected(null);
      }
      form.setFields(arrFormData);
    }
  }, [chitietDonViBHXH, danhSachDonViBHXH]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);
  const closeModal = useCallback(() => {
    setIsOpen(false);
    setchitietDonViBHXH(null);
    form.resetFields();
    setFilterParams(filterParams);
    layDanhSachDonViBHXH(filterParams);
  }, [filterParams]);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateDonViBHXHParams = form.getFieldsValue(); //lấy ra values của form
      console.log("values", values);

      const params = {
        ...values,

        // ma_ct: watchDLCha ? watchDLCha.value : null,
      };
      console.log("paraarram ", params);
      const response = await onUpdateDanhSachDonViBHXH(params);
      console.log("check respon ", response);
      if (response === -1) {
        console.log("cập nhật thành công");
        closeModal();
      } else {
        console.log("cập nhật thất bại");
      }
      //cập nhật lại danh mục đại lý
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER

  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => setIsOpen(false)} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Button type="primary" disabled={disableSubmit} iconPosition="end" onClick={() => onConfirm()} className="mr-2" icon={<CheckOutlined />}>
          Lưu
        </Button>
      </Form.Item>
    );
  };

  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI_CHI_TIET_DAI_LY[0].ma}}>
      {/* Row 1: Đội, Mã đại lý, Tên nguồn lực chính đại diện */}
      <Row gutter={16}>
        {/* {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac, disabled: chitietDaiLy ? true : false})} */}
        {renderFormInputColum({...ma, disabled: chitietDonViBHXH ? true : false})}
        {renderFormInputColum({...ten}, 16)}
      </Row>

      {/* Row 2: Tên đại lý, Loại đại lý, SĐT nguồn lực chính đại diện */}
      <Row gutter={16}>
        {renderFormInputColum({...dchi})}
        {renderFormInputColum({...dthoai})}
        {renderFormInputColum({...logo})}
      </Row>
      {/* Row 3: Email nguồn lực chính đại diện, Mã số thuế tổ chức, Trạng thái */}
      <Row gutter={16}>
        {renderFormInputColum({...mst})}
        {renderFormInputColum({...ten_e})}
        {renderFormInputColum({...ten_tat})}
      </Row>

      {/* Row 4: CMND/CCCD cá nhân, Thứ tự hiển thị */}
      <Row gutter={16}>
        {renderFormInputColum({
          ...ma_ct,
          options: [{ma: donViBHXHSelected?.ma, ten: donViBHXHSelected?.ten}],
          open: false,
          dropdownStyle: {display: "none"},
          labelInValue: true,
          onClick: () => refModelTimDaiLyCha.current?.open(),
        })}
        {renderFormInputColum({...stt})}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI_CHI_TIET_DAI_LY})}
      </Row>
    </Form>
  );

  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        className="modal-chi-tiet-don-vi-bhxh"
        maskClosable={false}
        title={<HeaderModal title={chitietDonViBHXH ? `Chi tiết đơn vị  ${chitietDonViBHXH.ten}` : "Tạo mới đơn vị "} trang_thai={chitietDonViBHXH?.trang_thai} />}
        // centered
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "60%",
          sm: "60%",
          md: "60%",
          lg: "60%",
          xl: "60%",
          xxl: "60%",
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
      <ModalThemDonViCha
        ref={refModelTimDaiLyCha}
        onSelectDonViBHXHCha={daiLyCha => {
          console.log("daiLyCha from ModalTimDaiLyCha", daiLyCha);
          if (daiLyCha) {
            form.setFieldValue("ma_ct", daiLyCha.ma);
            setDonViBHXHSelected(daiLyCha);
          } else {
            form.setFieldValue("ma_ct", null);
          }
        }}
        chiTietDonViBHXH={chitietDonViBHXH}
      />
    </Flex>
  );
});

ModalChiTietDonViBHXHComponent.displayName = "ModalChiTietDonViBHXHComponent";
export const ModalChiTietDonViBHXH = memo(ModalChiTietDonViBHXHComponent, isEqual);
