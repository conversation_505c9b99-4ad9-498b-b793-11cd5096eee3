import {CustomInputProps, IFormInput} from "@src/@types";
import strings from "@src/assets/strings";
import {DatePicker, Form, FormItemProps, InputNumber, TimePicker} from "antd";
import {Rule, RuleObject} from "antd/es/form";
import {isEqual} from "lodash";
import React, {memo, useCallback, useMemo} from "react";
import {NumericFormat} from "react-number-format";
import {twMerge} from "tailwind-merge";
import Checkbox from "../Checkbox";
import FormMessage from "../FormMessage";
import Input from "../Input";
import Select from "../Select";
import Switch from "../Switch";

const FormInputComponent: React.FC<IFormInput> = props => {
  const {component, errorMessage, normalize, allowNegative = false, ...etc} = props as CustomInputProps;
  const {name, label, valuePropName, className = "", initialValue, extra, rules, help} = etc as FormItemProps;
  const renderComponet = useCallback(() => {
    switch (component) {
      case "checkbox":
        return <Checkbox {...etc} />;
      case "switch":
        return <Switch {...etc} />;
      case "select":
        return (
          <Select
            showSearch={true}
            filterOption={(input, option) => {
              // input là từ khóa người dùng nhập
              // option là object chứa dữ liệu của mỗi option (bao gồm ten và ma)
              const ten = option?.ten?.toString().toLowerCase() || "";
              const ma = option?.ma?.toString().toLowerCase() || "";
              const searchText = input.toLowerCase();
              // Kiểm tra từ khóa có trong ten hoặc ma
              return ten.includes(searchText) || ma.includes(searchText);
            }}
            fieldNames={{
              label: "ten",
              value: "ma",
            }}
            {...etc}
          />
        );
      case "input-password":
        return <Input.Password autoComplete="current-password" {...etc} />;
      case "date-picker":
        return (
          <DatePicker
            format="DD/MM/YYYY"
            needConfirm={false} //không cần ấn nút Confirm
            placeholder="DD/MM/YYYY"
            // inputReadOnly // không nhập text được vào ngày giở
            {...etc}
            className={"block " + className}
          />
        );
      case "time-picker":
        return (
          <TimePicker
            format="HH:mm"
            placeholder="HH:mm"
            needConfirm={false} //không cần ấn nút Confirm
            // inputReadOnly // không nhập text được vào ngày giở
            {...etc}
            className={"block " + className}
          />
        );
      case "input-price": //nếu là input-price thì hiển thị dạng số có dấu phẩy ngăn cách hàng nghìn
        return <NumericFormat customInput={Input} thousandSeparator="," decimalSeparator="." decimalScale={2} allowNegative={allowNegative} allowClear={false} style={{textAlign: "right"}} {...etc} />;
      case "input-number": //nếu là input-number
        return <InputNumber {...etc} className={"block " + className} />;
      case "textarea":
        return <Input.TextArea {...etc} />;
      default:
        return <Input {...etc} />;
    }
  }, [component, etc]);

  const propName = useMemo(() => {
    if (component === "checkbox" || component === "switch") {
      return valuePropName || "checked";
    }
    return valuePropName;
  }, [component, valuePropName]);

  const formInputRules = useMemo(() => {
    if (rules) {
      return rules.map((item: Rule) => {
        const rule = new Object(item) as RuleObject;
        return {
          ...rule,
          message: rule.message ? <FormMessage message={rule.message} status="error" /> : <FormMessage message={errorMessage ?? strings().error_field_invalid} status="error" />,
        };
      });
    }
  }, [rules, errorMessage]);

  return (
    <Form.Item
      className={twMerge(`custom-form-input ${label ? "!mb-2" : ""}`, className)}
      name={name}
      label={label}
      valuePropName={propName}
      initialValue={initialValue}
      extra={extra && <FormMessage message={extra} />}
      rules={formInputRules}
      help={help}
      normalize={normalize}>
      {renderComponet()}
    </Form.Item>
  );
};

const FormInput = memo(FormInputComponent, isEqual);

export default FormInput;
