import {ReactQuery} from "@src/@types";

export interface NhomPhanCapDuyetContextProps {
  loading: boolean;
  tongSoDong: number;
  filterParams: ReactQuery.ITimKiemPhanTrangDanhSachNhomPhanCapDuyetParams & ReactQuery.IPhanTrang;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhSachNhomPhanCapDuyetParams & ReactQuery.IPhanTrang>>;
  onUpdateNhomPhanCapDuyet: (item: ReactQuery.IUpdateNhomPhanCapDuyetParams) => Promise<number | null | undefined>;
  layDanhSachNhomPhanCapDuyetPhanTrang: (params: ReactQuery.ITimKiemPhanTrangDanhSachNhomPhanCapDuyetParams) => void;
  danhSachNhomPhanCapDuyetPhanTrang: Array<CommonExecute.Execute.INhomPhanCapDuyet>;
  layChiTietNhomPhanCapDuyet: (params: ReactQuery.IChiTietNhomPhanCapDuyetParams) => Promise<CommonExecute.Execute.INhomPhanCapDuyet | null>;
}
