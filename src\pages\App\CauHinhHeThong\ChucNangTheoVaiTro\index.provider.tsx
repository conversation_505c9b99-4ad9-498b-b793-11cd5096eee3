import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import React, {PropsWithChildren, useEffect, useMemo, useCallback} from "react";
import {useState} from "react";
import {ChucNangTheoVaiTroContextProps} from "./index.model";
import {ChucNangTheoVaiTroContext} from "./index.context";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
// import {defaultFormValue} from "./index.configs";
import {message} from "antd";

const ChucNangTheoVaiTroProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  console.log("Danh mục sản phẩm PROVIDER", children);
  const mutateUseCommonExecute = useCommonExecute();
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [chiTietChucNangTheoVaiTro, setChiTietChucNangTheoVaiTro] = useState<CommonExecute.Execute.IChiTietChucNangTheoVaiTro>({});
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [danhSachChucNangTheoVaiTroPhanTrang, setDanhSachChucNangTheoVaiTro] = useState<Array<CommonExecute.Execute.IChucNangTheoVaiTro>>([]);
  const [listChucNangChuaPhanVaiTro, setListChucNangChuaPhanVaiTro] = useState<Array<CommonExecute.Execute.IChiTietChucNang>>([]);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhSachChucNangTheoVaiTroParams & ReactQuery.IPhanTrang>({
    ma: "",
    ten: "",

    trang_thai: "",
    // trang: 1,
    // so_dong: 20,
  });
  useEffect(() => {
    initData();
  }, []);
  const initData = () => {
    layDanhSachChucNangTheoVaiTroPhanTrang(filterParams);
    // layDanhSachChucNangChuaPhanVaiTro();
  };
  useEffect(() => {
    layDanhSachChucNangTheoVaiTroPhanTrang(filterParams);
  }, [filterParams]);
  /* ĐỐI TÁC */
  //   const getListDoiTac = useCallback(async () => {
  //     try {
  //       const response = await mutateUseCommonExecute.mutateAsync({
  //         actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
  //       });
  //       setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
  //     } catch (error) {
  //       console.log("getListDoiTac error ", error);
  //     }
  //   }, [mutateUseCommonExecute]);

  const layDanhSachChucNangTheoVaiTroPhanTrang = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangDanhSachChucNangTheoVaiTroParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_VAI_TRO_CHUC_NANG,
        };

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);

        const data = response.data.data;
        console.log("data", data);
        setDanhSachChucNangTheoVaiTro(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("Lấy danh sách chức năng theo vai trò error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy chi tiết 1 sản phẩm
  const layChiTietChucNangTheoVaiTro = useCallback(
    async (item: ReactQuery.IChiTietChucNangTheoVaiTroParams): Promise<CommonExecute.Execute.IChiTietChucNangTheoVaiTro | null> => {
      try {
        const params = {
          ma: item.ma,
          actionCode: ACTION_CODE.CHI_TIET_VAI_TRO_CHUC_NANG,
        };
        console.log("params lấy chi tiết chức năng theo vai trò", params);
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        console.log("responseData chi tiết", responseData);
        setChiTietChucNangTheoVaiTro(responseData.data as CommonExecute.Execute.IChiTietChucNangTheoVaiTro);
        return responseData.data as CommonExecute.Execute.IChiTietChucNangTheoVaiTro;
      } catch (error: any) {
        console.log("layChiTietChucNangTheoVaiTro error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  const onUpdateChucNangTheoVaiTro = useCallback(
    async (body: ReactQuery.ICapNhatChucNangTheoVaiTroParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_VAI_TRO_CHUC_NANG,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          initData();
          return responseData.data as unknown as number;
        }
      } catch (error: any) {
        console.log("onUpdateChucNangTheoVaiTro error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Xóa chức năng ra khỏi vai trò
  const onDeleteChucNangTheoVaiTro = useCallback(
    async (body: ReactQuery.IDeleteChucNangTheoVaiTroParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.XOA_CHUC_NANG_VAI_TRO,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Xóa chức năng thành công!");
          initData();
          return responseData.data as unknown as number;
        }
      } catch (error: any) {
        console.log("onDeleteChucNangTheoVaiTro error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //lấy danh sách chức năng chưa phân vai trò
  const layDanhSachChucNangChuaPhanVaiTro = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_CHUC_NANG_CHUA_PHAN_VT,
      });
      setListChucNangChuaPhanVaiTro(response?.data);
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);

  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<ChucNangTheoVaiTroContextProps>(
    () => ({
      loading: mutateUseCommonExecute.isLoading,
      tongSoDong,
      // defaultFormValue,
      filterParams,
      danhSachChucNangTheoVaiTroPhanTrang,
      chiTietChucNangTheoVaiTro,
      onDeleteChucNangTheoVaiTro,
      setFilterParams,
      layDanhSachChucNangTheoVaiTroPhanTrang,
      layChiTietChucNangTheoVaiTro,
      onUpdateChucNangTheoVaiTro,
      listChucNangChuaPhanVaiTro,
      layDanhSachChucNangChuaPhanVaiTro,
      setListChucNangChuaPhanVaiTro,
      setChiTietChucNangTheoVaiTro,
    }),
    [
      danhSachChucNangTheoVaiTroPhanTrang,
      tongSoDong,
      mutateUseCommonExecute,
      filterParams,
      chiTietChucNangTheoVaiTro,
      onDeleteChucNangTheoVaiTro,
      setFilterParams,
      layDanhSachChucNangTheoVaiTroPhanTrang,
      layChiTietChucNangTheoVaiTro,
      onUpdateChucNangTheoVaiTro,
      listChucNangChuaPhanVaiTro,
      layDanhSachChucNangChuaPhanVaiTro,
      setListChucNangChuaPhanVaiTro,
      setChiTietChucNangTheoVaiTro,
    ],
  );

  return <ChucNangTheoVaiTroContext.Provider value={value}>{children}</ChucNangTheoVaiTroContext.Provider>;
};
export default ChucNangTheoVaiTroProvider;
