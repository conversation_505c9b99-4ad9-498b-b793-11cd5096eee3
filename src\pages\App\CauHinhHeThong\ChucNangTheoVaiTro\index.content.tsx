// import { useMemo } from "react";
import {ClearOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {Col, Flex, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {useCallback, useMemo, useRef, useState} from "react";

import {ChucNangTheoVaiTroColumns, DataIndex, FormTimKiemChucNangTheoVaiTro, radioItemTrangThaiChucNangTheoVaiTroTable, TableChucNangTheoVaiTroDataType, TRANG_THAI} from "./index.configs";
import {useChucNangTheoVaiTroContext} from "./index.context";
import {ReactQuery} from "@src/@types";

import "./index.default.scss";
import {IModalChiTietChucNangTheoVaiTroRef} from "./Component/index.configs";
import {ModalChiTietChucNangTheoVaiTro} from "./Component/ModalChiTietChucNangTheoVaiTro";
// import {IModalChiTietChucNangTheoVaiTroRef, NGHIEP_VU_SAN_PHAM} from "./Component/index.configs";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";

const {ma, ten, trang_thai} = FormTimKiemChucNangTheoVaiTro;
const ChucNangTheoVaiTroContent: React.FC = () => {
  const {loading, tongSoDong, filterParams, setFilterParams, danhSachChucNangTheoVaiTroPhanTrang, layDanhSachChucNangTheoVaiTroPhanTrang, layChiTietChucNangTheoVaiTro} =
    useChucNangTheoVaiTroContext();
  const searchInput = useRef<InputRef>(null);
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangDanhSachChucNangTheoVaiTroParams>(filterParams);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPaginationTableProps.defaultPageSize);
  const refModalChiTietChucNangTheoVaiTro = useRef<IModalChiTietChucNangTheoVaiTroRef>(null);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);
  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );
  const onSearchApi = (values: ReactQuery.ITimKiemPhanTrangDanhSachChucNangTheoVaiTroParams & ReactQuery.IPhanTrang) => {
    const cleanedValues = {
      ...values,
      ma: values.ma ?? "",
      ten: values.ten ?? "",
      trang_thai: values.trang_thai ?? "",
      trang: values.trang ?? 1,
      so_dong: values.so_dong ?? 15,
    };
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    // layDanhSachChucNangTheoVaiTroPhanTrang({...cleanedValues, trang: 1, so_dong: pageSize});
    setFilterParams(cleanedValues);
  };
  const dataTableListChucNangTheoVaiTro = useMemo<Array<TableChucNangTheoVaiTroDataType>>(() => {
    try {
      const tableData = danhSachChucNangTheoVaiTroPhanTrang.map((item: any, index: number) => {
        return {
          stt: item.sott,
          ma: item.ma,
          ten: item.ten,
          ngay_tao: item.ngay_tao,
          nguoi_tao: item.nguoi_tao,
          ngay_cap_nhat: item.ngay_cap_nhat,
          nguoi_cap_nhat: item.nguoi_cap_nhat,
          trang_thai: item.trang_thai,
          trang_thai_ten: item.trang_thai_ten,
          key: index.toString(),
        };
      });
      const arrEmptyRow: Array<TableChucNangTheoVaiTroDataType> = fillRowTableEmpty(tableData.length, defaultPaginationTableProps.defaultPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListChucNangTheoVaiTro error", error);
      return [];
    }
  }, [danhSachChucNangTheoVaiTroPhanTrang]);
  const onChangePage = (page: number, pageSize: number) => {
    // console.log("Gọi API với :", {trang: page, so_dong: pageSize});
    setPage(page);
    setPageSize(pageSize);
    setFilterParams({...filterParams, trang: page, so_dong: pageSize});
    layDanhSachChucNangTheoVaiTroPhanTrang({...searchParams, trang: page, so_dong: pageSize});
  };
  //Render
  //tạo cột tìm kiếm
  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TableChucNangTheoVaiTroDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiChucNangTheoVaiTroTable : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  const renderFormInputColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  //render header table tìm kiếm
  const renderHeaderTableChucNangTheoVaiTro = () => {
    return (
      <div>
        <Form initialValues={{trang_thai: TRANG_THAI[0].ma}} layout="vertical" className="[&_.ant-form-item]:!mb-0" onFinish={onSearchApi}>
          <div className="flex flex-col gap-5">
            <Row gutter={16} align={"bottom"}>
              {renderFormInputColum({...ma})}
              {renderFormInputColum(ten)}

              {renderFormInputColum({...trang_thai, options: TRANG_THAI})}
              <Col span={2}>
                <Form.Item>
                  <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="mb-0 w-full">
                    Tìm kiếm
                  </Button>
                </Form.Item>
              </Col>
              <Col span={2}>
                <Form.Item>
                  <Flex wrap="wrap" gap="small" className="w-full">
                    <Button className="w-full" type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalChiTietChucNangTheoVaiTro.current?.open()} loading={loading}>
                      Tạo mới
                    </Button>
                  </Flex>
                </Form.Item>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
    );
  };

  //render table
  return (
    <div id={ID_PAGE.CHUC_NANG_THEO_VAI_TRO} className="[&_.ant-space]:w-full">
      <Table<TableChucNangTheoVaiTroDataType>
        {...defaultTableProps}
        loading={loading}
        onRow={(record, rowIndex) => {
          return {
            onClick: async event => {
              if (record.key.toString().includes("empty")) return;
              const chiTietChucNangTheoVaiTro = await layChiTietChucNangTheoVaiTro({ma: record.ma});
              console.log("chiTietChucNangTheoVaiTro", chiTietChucNangTheoVaiTro);
              if (chiTietChucNangTheoVaiTro) {
                refModalChiTietChucNangTheoVaiTro.current?.open(chiTietChucNangTheoVaiTro);
              }
            },
          };
        }}
        columns={(ChucNangTheoVaiTroColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableChucNangTheoVaiTroDataType, item.title) : {}),
          };
        })}
        dataSource={dataTableListChucNangTheoVaiTro}
        title={renderHeaderTableChucNangTheoVaiTro}
        pagination={{
          ...defaultPaginationTableProps,
          // defaultPageSize: 15,
          total: tongSoDong,
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
          // showLessItems: true, //Hiển thị ít mục trang hơn
          // defaultPageSize: 2, //Số lượng item dữ liệu mặc định trên mỗi trang
          // onShowSizeChange : Được gọi khi pageSize thay đổi
          // showTitle: false,// Hiển thị tiêu đề của item trang
          //showTotal : hiển thị UI bao nhiêu / bao nhiêu bản ghi
        }}
      />
      <ModalChiTietChucNangTheoVaiTro ref={refModalChiTietChucNangTheoVaiTro} />
    </div>
  );
};

export default ChucNangTheoVaiTroContent;
