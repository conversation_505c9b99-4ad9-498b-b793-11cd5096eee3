import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
// import {IModalchiTietDoiTuongRef, Props, TRANG_THAI} from "./index.configs";
import {Col, Flex, Form, Modal, Row} from "antd";
import {useBaoHiemTaiSanContext} from "../../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal} from "@src/components";
import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {FormChiTietDoiTuongDS, IModalchiTietDoiTuongRef, PropsDoiTuong} from "./Constant";
const {ma, ten, dchi, tinh_thanh, phuong_xa, latitude, longitude} = FormChiTietDoiTuongDS;

const ModalChiTietDoiTuongDSComponent = forwardRef<IModalchiTietDoiTuongRef, PropsDoiTuong>(({}: PropsDoiTuong, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDoiTuong?: CommonExecute.Execute.IDoiTuong) => {
      if (dataDoiTuong) {
        setChiTietDoiTuong(dataDoiTuong);
      } else {
        setChiTietDoiTuong(undefined as unknown as CommonExecute.Execute.IDoiTuong);
        form.resetFields();
      }
      setIsOpen(true);
    },
    close: () => setIsOpen(false),
  }));
  const [isOpen, setIsOpen] = useState(false);

  const {loading, updateDoiTuong, filterParams, setFilterParams, timKiemPhanTrangDanhSachDoiTuong, getListPhuongXa, chiTietDoiTuong, setChiTietDoiTuong, listTinhThanh, listPhuongXa} =
    useBaoHiemTaiSanContext();
  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  useEffect(() => {
    if (chiTietDoiTuong) {
      const arrFormData = [];

      for (const key in chiTietDoiTuong) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IDoiTuong,
          value: chiTietDoiTuong[key as keyof CommonExecute.Execute.IDoiTuong],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDoiTuong, form]);
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);
  const closeModal = () => {
    setIsOpen(false);
    setChiTietDoiTuong(undefined as unknown as CommonExecute.Execute.IDoiTuong);
    form.resetFields();
    setFilterParams(filterParams);
  };
  const handleChangeTinhThanh = useCallback(
    (value: string) => {
      form.setFieldValue("phuong_xa", undefined);
      getListPhuongXa({ma_tinh: value});
    },
    [getListPhuongXa],
  );
  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateDoiTuongParams = form.getFieldsValue(); //lấy ra values của form
      if (!chiTietDoiTuong) {
        values.ma = "";
        values.longitude = Number(values.longitude);
        values.latitude = Number(values.latitude);
      }

      const response = await updateDoiTuong(values);

      if (response === -1) {
        console.log("cập nhật thành công");
        timKiemPhanTrangDanhSachDoiTuong(filterParams);
        setIsOpen(false);
      } else {
        console.log("cập nhật thất bại");
      }
    } catch (error) {
      console.log("onConfirm", error);
    }
  };
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  //render footer
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => setIsOpen(false)} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Button type="primary" disabled={disableSubmit} iconPosition="end" onClick={() => onConfirm()} className="mr-2" icon={<CheckOutlined />}>
          Lưu
        </Button>
      </Form.Item>
    );
  };
  const renderForm = () => (
    <Form form={form} layout="vertical">
      {/* MÃ */}
      <Row gutter={16}>
        {renderFormInputColum({...ma})}
        {renderFormInputColum({...ten})}
        {renderFormInputColum({...dchi})}
      </Row>
      <Row gutter={16}>
        {renderFormInputColum({...tinh_thanh, options: listTinhThanh, onChange: handleChangeTinhThanh})}
        {renderFormInputColum({...phuong_xa, options: listPhuongXa})}
        {renderFormInputColum({...latitude}, 4)}
        {renderFormInputColum({...longitude}, 4)}
      </Row>
    </Form>
  );
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietDoiTuong ? `Chi tiết đối tượng ${chiTietDoiTuong.ten}` : "Tạo mới đối tượng"} />}
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "55%",
          sm: "55%",
          md: "55%",
          lg: "55%",
          xl: "55%",
          xxl: "55%",
        }}
        footer={renderFooter}
        className="[&_.ant-space]:w-full">
        {renderForm()}
      </Modal>
    </Flex>
  );
});
ModalChiTietDoiTuongDSComponent.displayName = "ModalchiTietDoiTuongComponent";
export const ModalchiTietDoiTuong = memo(ModalChiTietDoiTuongDSComponent, isEqual);
