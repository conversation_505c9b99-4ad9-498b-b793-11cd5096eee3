import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {defaultPaginationTableProps} from "@src/hooks";
import {useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";
import {TableTinhThanhColumnDataType} from "./index.configs";
import {QuanLyTinhThanhContext} from "./index.context";
import {IQuanLyTinhThanhContextProps} from "./index.model";

const QuanLyTinhThanhProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();

  const [listTinhThanh, setListTinhThanh] = useState<Array<CommonExecute.Execute.IDanhMucTinhThanh>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucTinhThanhParams & ReactQuery.IPhanTrang>({
    ngay_ad: undefined,
    ma: "",
    ten: "",
    trang_thai: "",
    trang: 1,
    so_dong: defaultPaginationTableProps.defaultPageSize,
  });

  //khởi tạo dữ liệu ban đầu
  useEffect(() => {
    initData();
  }, []);

  const getChiTietTinhThanh = useCallback(
    async (data: TableTinhThanhColumnDataType) => {
      // Lấy thông tin chi tiết của một tỉnh thành khi click vào row
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.ma,ngay_ad:data.ngay_ad,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_TINH_THANH,
        }as any);
        
        // Response structure confirmed: data.lke[0]
        const responseData = response.data as any;
        if (responseData?.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          return responseData.lke[0] as CommonExecute.Execute.IDanhMucTinhThanh;
        }
        
        return {} as CommonExecute.Execute.IDanhMucTinhThanh;
      } catch (error) {
        message.error("Không thể tải chi tiết tỉnh thành");
        return {} as CommonExecute.Execute.IDanhMucTinhThanh;
      }
    },
    [mutateUseCommonExecute],
  );

  const getListTinhThanh = useCallback(async () => {
    // Tìm kiếm và load danh sách tỉnh thành theo filter parameters
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_TINH_THANH,
      } as any);
      
      // Response structure confirmed: data.data[] + data.tong_so_dong
      const responseData = response.data;
      if (responseData?.data && Array.isArray(responseData.data)) {
        setListTinhThanh(responseData.data);
        setTongSoDong(responseData.tong_so_dong || responseData.data.length);
      }
        } catch (error) {
      message.error("Không thể tải danh sách tỉnh thành");
        }
  }, [mutateUseCommonExecute, filterParams]);

  useEffect(() => {
    getListTinhThanh();
  }, [filterParams]);

  const capNhatChiTietTinhThanh = useCallback(
    async (data: ReactQuery.ICapNhatDanhMucTinhThanhParams) => {
      // Tạo mới hoặc cập nhật thông tin tỉnh thành
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
          actionCode: ACTION_CODE.UPDATE_DANH_MUC_TINH_THANH,
        } as any);
        message.success(data.ma ? "Cập nhật tỉnh thành thành công" : "Thêm mới tỉnh thành thành công");
        return response.data;
      } catch (error) {
        message.error(data.ma ? "Có lỗi xảy ra khi cập nhật tỉnh thành" : "Có lỗi xảy ra khi thêm mới tỉnh thành");
      }
    },
    [mutateUseCommonExecute],
  );

  //initData chỉ dùng trong useEffect 1 lần, không truyền đi đâu cả, nên không cần memo hoá
  const initData = () => {
    // Khởi tạo dữ liệu ban đầu khi component mount (hiện tại không cần load gì)
  };

  const value = useMemo<IQuanLyTinhThanhContextProps>(
    () => ({
    listTinhThanh,
    tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
    filterParams,
    getListTinhThanh,
    getChiTietTinhThanh,
    capNhatChiTietTinhThanh,
    setFilterParams,
    }),
    [listTinhThanh, tongSoDong, mutateUseCommonExecute, filterParams, getListTinhThanh, getChiTietTinhThanh, capNhatChiTietTinhThanh],
  );

  return <QuanLyTinhThanhContext.Provider value={value}>{children}</QuanLyTinhThanhContext.Provider>;
};

export default QuanLyTinhThanhProvider;
/**
 * - Quản lý state toàn cục cho module quản lý tỉnh thành
 * - Cung cấp các method để thao tác với API tỉnh thành
 * - Xử lý logic nghiệp vụ: tìm kiếm, phân trang, CRUD
 * - Chia sẻ dữ liệu giữa các component con thông qua Context
 */