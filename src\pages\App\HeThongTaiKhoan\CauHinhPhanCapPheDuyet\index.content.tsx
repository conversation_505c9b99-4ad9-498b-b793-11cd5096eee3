import {ClearOutlined, SearchOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty, usePhongBan} from "@src/hooks";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import type {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import React, {memo, useCallback, useMemo, useRef, useState} from "react";
// import Highlighter from "react-highlight-words";
// import ModalChiTietTaiKhoanNguoiDung, {IModalChiTietTaiKhoanNguoiDungRef} from "./Component/ModalChiTietTaiKhoanNguoiDung";
import {
  FormTimKiemPhanTrangTaiKhoanNguoiDung,
  optionTrangThaiTaiKhoanNguoiDungSelect,
  radioItemTrangThaiTaiKhoanNSDTable,
  TableTaiKhoanNguoiDungDataType,
  taiKhoanNguoiDungColumns,
} from "./index.configs";
import {useCauHinhPhanCapPheDuyetContext} from "./index.context"; // file này lưu biến về state

import "./index.default.scss";
import ModalChiTietCauHinhPhanCapPheDuyet, {IModalChiTietCauHinhPhanCapPheDuyetRef} from "./Component/ModalChiTietCauHinhPhanCapPheDuyet";

type DataIndex = keyof TableTaiKhoanNguoiDungDataType;

const CauHinhPhanCapPheDuyetContent: React.FC = () => {
  // const listChiNhanh = useChiNhanh();
  const listPhongBan = usePhongBan();
  const {danhSachTaiKhoanNguoiDung, loading, layDanhSachTaiKhoanNguoiDungPhanTrang, listChiNhanh, tongSoDong, defaultFormValue, listDoiTac, getListChiNhanhTheoDoiTac, layChiTietTaiKhoanNguoiDung} =
    useCauHinhPhanCapPheDuyetContext();
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [searchParams, setSearchParams] = useState<ReactQuery.ILayDanhSachPhongBanPhanTrangParams>(defaultFormValue);
  // const [form] = Form.useForm;
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPaginationTableProps.defaultPageSize);
  const refModalChiTietCauHinhPhanCapPheDuyet = useRef<IModalChiTietCauHinhPhanCapPheDuyetRef>(null);
  const [filteredChiNhanh, setFilteredChiNhanh] = useState<Array<CommonExecute.Execute.IChiNhanh>>([]);
  const [filteredPhongBan, setFilteredPhongBan] = useState<Array<CommonExecute.Execute.IDanhMucPhongBan>>([]);
  const {ma_chi_nhanh, phong, nd_tim, ma_doi_tac} = FormTimKiemPhanTrangTaiKhoanNguoiDung;
  // const watchDoiTac = Form.useWatch("ma_doi_tac", form);
  const dataTableListPhongBan = useMemo<Array<TableTaiKhoanNguoiDungDataType>>(() => {
    try {
      const tableData = danhSachTaiKhoanNguoiDung.map((item, index) => {
        return {
          ten: item.ten,
          email: item.email,
          dthoai: item.dthoai,
          phong: item.phong,
          phong_ten: item.phong_ten,
          ma_doi_tac: item.ma_doi_tac,
          ma_chi_nhanh: item.ma_chi_nhanh,
          ma: item.ma,
          stt: item.sott,
          sott: item.sott,
          trang_thai_ten: item.trang_thai_ten,
          trang_thai: item.trang_thai,
          chi_nhanh_ten_tat: item.chi_nhanh_ten_tat,
          doi_tac_ten_tat: item.doi_tac_ten_tat,
          ngay_tao: item.ngay_tao,
          nguoi_tao: item.nguoi_tao,
          ngay_cap_nhat: item.ngay_cap_nhat,
          nguoi_cap_nhat: item.nguoi_cap_nhat,
          ngay_kt: item.ngay_kt,
          ngay_hl: item.ngay_hl,
          ten_chuc_danh: item.ten_chuc_danh,
          key: index.toString(),
        };
      });
      const arrEmptyRow: Array<TableTaiKhoanNguoiDungDataType> = fillRowTableEmpty(tableData.length, defaultPaginationTableProps.defaultPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListPhongBan error", error);
      return [];
    }
  }, [danhSachTaiKhoanNguoiDung]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );
  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TableTaiKhoanNguoiDungDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiTaiKhoanNSDTable : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        // Xác định màu dựa vào text (trạng thái hiển thị)
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return <Tag color={color}>{text}</Tag>;
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  //Bấm tìm kiếm
  const onSearchApi = (values: ReactQuery.ILayDanhSachTaiKhoanNguoiDungPhanTrangParams & ReactQuery.IPhanTrang) => {
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,
      ma_doi_tac: values.ma_doi_tac ?? "",
      ma_chi_nhanh: values.ma_chi_nhanh ?? "",
      trang_thai: values.trang_thai ?? "",
      phong: values.phong ?? "",
      nd_tim: values.nd_tim ?? "",
    };
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    layDanhSachTaiKhoanNguoiDungPhanTrang({...cleanedValues, trang: 1, so_dong: pageSize});
  };

  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      setPage(page);
      setPageSize(pageSize);
      layDanhSachTaiKhoanNguoiDungPhanTrang({...searchParams, trang: page, so_dong: pageSize});
    },
    [searchParams],
  );
  const handleChangeDoiTac = (value: any) => {
    // Lọc danh sách phòng ban theo mã chi nhánh
    const maDoiTac = typeof value === "string" ? value : "";
    getListChiNhanhTheoDoiTac();
    const filtered = listChiNhanh.filter(pb => pb.ma_doi_tac === maDoiTac);
    setFilteredChiNhanh(filtered);
    return filtered;
  };
  const handleChangeChiNhanh = (value: any) => {
    const maChiNhanh = typeof value === "string" ? value : "";
    // Lọc danh sách phòng ban theo mã chi nhánh
    const filtered = listPhongBan.listPhongBan.filter(pb => pb.ma_chi_nhanh === maChiNhanh);
    setFilteredPhongBan(filtered);
  };

  // RENDER

  //form input
  const renderFormInputColum = (props: IFormInput, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  //render header table
  const renderHeaderTableCauHinhPhanCapPheDuyet = () => {
    return (
      <div>
        <Form initialValues={{trang_thai: optionTrangThaiTaiKhoanNguoiDungSelect[0].ma}} layout="vertical" className="[&_.ant-form-item]:!mb-0" onFinish={onSearchApi}>
          <div>
            <Row gutter={16} align={"bottom"}>
              {renderFormInputColum(
                {
                  ...ma_doi_tac,
                  options: listDoiTac,
                  onChange: handleChangeDoiTac,
                },
                5,
              )}
              {renderFormInputColum({...ma_chi_nhanh, options: filteredChiNhanh, onChange: handleChangeChiNhanh}, 5)}
              {renderFormInputColum({...phong, options: filteredPhongBan}, 5)}
              {renderFormInputColum({...nd_tim}, 5)}

              <Col span={2}>
                {/* <Form.Item> */}
                <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="mb-0 w-full">
                  Tìm kiếm
                </Button>
                {/* </Form.Item> */}
              </Col>
              {/* <Col span={2}>
               
                <Button className="w-full" type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalChiTietNguoiSuDung?.current?.open()}>
                  Tạo mới
                </Button>
              
              </Col> */}
            </Row>
          </div>
        </Form>
      </div>
    );
  };

  //render table
  return (
    <div id={ID_PAGE.CAU_HINH_PHAN_CAP_PHE_DUYET} className="[&_.ant-space]:w-full">
      <Table<TableTaiKhoanNguoiDungDataType>
        {...defaultTableProps}
        // sticky
        // className="antd-table-hide-scroll"

        loading={loading}
        onRow={(record, rowIndex) => {
          return {
            style: {cursor: "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: async event => {
              // setOpenResponsive(true)
              // onClickXemChiTietPhongBan(record)
              if (record.key.toString().includes("empty")) return;
              const response: CommonExecute.Execute.IChiTietNguoiSuDung | null = await layChiTietTaiKhoanNguoiDung(record);
              console.log("response", response);
              if (response?.ma) refModalChiTietCauHinhPhanCapPheDuyet.current?.open(response);
            }, // click row
            // onDoubleClick: event => { }, // double click row
            // onContextMenu: event => { }, // right button click row
            // onMouseEnter: event => { }, // mouse enter row
            // onMouseLeave: event => { }, // mouse leave row
          };
        }}
        title={renderHeaderTableCauHinhPhanCapPheDuyet}
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong, //quan trọng - để hiển thị toàn bộ số lượng dòng dữ liệu phục vụ việc phân trang

          pageSize: pageSize, //Số lượng mục dữ liệu trên mỗi trang

          //được gọi khi page size change
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
        }}
        // pagination={false}
        columns={(taiKhoanNguoiDungColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableTaiKhoanNguoiDungDataType, item.title) : {}),
          };
        })} //định nghĩa cột của table
        dataSource={dataTableListPhongBan}
        bordered
      />

      <ModalChiTietCauHinhPhanCapPheDuyet ref={refModalChiTietCauHinhPhanCapPheDuyet} />
    </div>
  );
};

CauHinhPhanCapPheDuyetContent.displayName = "CauHinhPhanCapPheDuyetContent"; //Được sử dụng trong các thông báo gỡ lỗi

export default memo(CauHinhPhanCapPheDuyetContent, isEqual);
