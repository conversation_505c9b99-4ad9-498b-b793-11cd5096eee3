import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";

import {QuanLyTaiKhoanDonViThuHoContext} from "./index.context";
import {IQuanLyTaiKhoanDonViThuHoContextProps} from "./index.model";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
import {TableTaiKhoanDonViThuHoColumnDataType} from "./index.configs";
import {useCommonExecute} from "@src/services/react-queries";
import {useProfile} from "@src/hooks";
import {message} from "antd";

const QuanLyTaiKhoanDonViThuHoProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const {profile} = useProfile();

  const [listTaiKhoanDonViThuHo, setListTaiKhoanDonViThuHo] = useState<Array<CommonExecute.Execute.ITaiKhoanDonViThuHo>>([]);
  const [listDonVi, setListDonVi] = useState<Array<CommonExecute.Execute.IDanhSachDonViBHXH>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangTaiKhoanDonViThuHoParams & ReactQuery.IPhanTrang>({ma: "", ten: "", trang_thai: "", trang: 1, so_dong: 20});
  
  //khởi tạo dữ liệu ban đàu
  useEffect(() => {//nè
    initData();
  }, []);

  //Tác dụng: Lấy danh sách đơn vị để hiển thị trong dropdown filter
  const getListDonVi = useCallback(async () => {
          try {
              const response = await mutateUseCommonExecute.mutateAsync({
                  ma: "",
                  ten: "",
                  trang_thai: "",
                  trang: 1,
                  so_dong: 100,
                  actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DON_VI_THU_HO_BHXH,
              } as any);
  
              if (response.data) {
                  console.log("getListDonVi response.data:", response.data);
                  //Xử lý response structure để đảm bảo tương thích
                  if (Array.isArray(response.data)) {
                      setListDonVi(response.data);
                  } else if (response.data && typeof response.data === "object") {
                      const responseData = response.data as any;
                      if (responseData.data && Array.isArray(responseData.data)) {
                          setListDonVi(responseData.data);
                      } else {
                          setListDonVi([]);
                      }
                  }
              } else {
                  console.log("response.data is null/undefined");
              }
          } catch (error) {
              console.log("[Provider] getListDonVi error:", error);
              setListDonVi([]);
          }
}, [mutateUseCommonExecute]);

  const getChiTietTaiKhoanDonViThuHo = useCallback(
    async (data: TableTaiKhoanDonViThuHoColumnDataType) => {
      try {
        console.log("[Provider] getChiTietTaiKhoanDonViThuHo được gọi với data:", data);
        
        if (!data.ma) {
          console.log("[Provider] Không có mã để tìm kiếm, data.ma =", data.ma);
          return {} as CommonExecute.Execute.ITaiKhoanDonViThuHo;
        }
      
        console.log(" [Provider] Gọi API với params:", {
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_TAI_KHOAN_DON_VI_THU_HO_BHXH
        });
        
        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_TAI_KHOAN_DON_VI_THU_HO_BHXH,
        } as any);
        
        console.log("[Provider] Raw response từ API:", response);
        console.log("[Provider] Response.data:", response.data);
        
        // Xử lý structure response.data.lke
        const responseData = response.data as any; // Type casting để truy cập lke
        if (responseData && responseData.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          const result = responseData.lke[0] as CommonExecute.Execute.ITaiKhoanDonViThuHo;
          console.log("[Provider] Trả về data từ lke[0]:", result);
          return result;
        } else {
          console.log("[Provider] Không tìm thấy data trong response.data.lke");
          return {} as CommonExecute.Execute.ITaiKhoanDonViThuHo;
        }
      } catch (error) {
        console.log("[Provider] getChiTietTaiKhoanDonViThuHo error:", error);
        return {} as CommonExecute.Execute.ITaiKhoanDonViThuHo;
      }
    },
    [mutateUseCommonExecute],
  );

  const getListTaiKhoanDonViThuHo = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_TAI_KHOAN_DON_VI_THU_HO_BHXH,
      } as any);
      if (response.data) {
        setListTaiKhoanDonViThuHo(response.data.data);
        setTongSoDong(response.data.tong_so_dong);
      }
    } catch (error) {
      console.log("getListTaiKhoanDonViThuHo error ", error);
    }
  }, [mutateUseCommonExecute]);

  useEffect(() => {
    getListTaiKhoanDonViThuHo();
  }, [filterParams]);

  const capNhatChiTietTaiKhoanDonViThuHo = useCallback(
    async (data: ReactQuery.ICapNhatTaiKhoanDonViThuHoParams, isEditMode: boolean = false) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
          actionCode: ACTION_CODE.UPDATE_TAI_KHOAN_DON_VI_THU_HO_BHXH,
        } as any);
        message.success(isEditMode ? "Cập nhật Tài khoản đơn vị thu hộ  thành công" : "Thêm mới Tài khoản đơn vị thu hộ  thành công");
        return response.data;
      } catch (error) {
        message.error(isEditMode ? "Có lỗi xảy ra khi cập nhật Tài khoản đơn vị thu hộ " : "Có lỗi xảy ra khi thêm mới Tài khoản đơn vị thu hộ ");
        console.log("capNhatChiTietTaiKhoanDonViThuHo err", error);
        // return {} as CommonExecute.Execute.ITaiKhoanDonViThuHo;
      }
    },
    [mutateUseCommonExecute],
  );

  // API lấy danh sách tài khoản quản lý
  const layDanhSachTaiKhoanQuanLy = useCallback(
    async (maTaiKhoanCha: string) => {
      try {
        console.log("[Provider] layDanhSachTaiKhoanQuanLy được gọi với maTaiKhoanCha:", maTaiKhoanCha);

        // Tìm thông tin tài khoản cha từ danh sách hiện tại
        const taiKhoanCha = listTaiKhoanDonViThuHo.find(tk => tk.ma === maTaiKhoanCha);
        if (!taiKhoanCha) {
          console.log("[Provider] Không tìm thấy thông tin tài khoản cha:", maTaiKhoanCha);
          return [];
        }

        console.log("[Provider] Thông tin tài khoản cha:", {
          ma: taiKhoanCha.ma,
          ma_dvi: taiKhoanCha.ma_dvi
        });
        console.log("[Provider] Profile info:", {
          ma_doi_tac: profile.nsd.ma_doi_tac,
          ma_chi_nhanh: profile.nsd.ma_chi_nhanh,
          ma: profile.nsd.ma
        });

        const response = await mutateUseCommonExecute.mutateAsync({
          // Truyền đầy đủ tham số theo stored procedure PBHXH_DOI_TAC_NSD_QL_LKE
          ma_doi_tac_nsd: profile.nsd.ma_doi_tac, // Sử dụng ma_doi_tac từ profile
          ma_chi_nhanh_nsd: taiKhoanCha.ma_dvi, // Sử dụng ma_dvi của tài khoản cha để tìm các tài khoản cùng đơn vị
          nsd: profile.nsd.ma,
          pas: profile.pw || "",
          ma: maTaiKhoanCha,
          actionCode: ACTION_CODE.LIET_KE_TAI_KHOAN_QUAN_LY_THU_HO_BHXH,
        } as any);

        console.log("[Provider] layDanhSachTaiKhoanQuanLy response:", response);
        console.log("[Provider] response.data type:", typeof response.data);
        console.log("[Provider] response.data isArray:", Array.isArray(response.data));
        console.log("[Provider] response.data content:", JSON.stringify(response.data));

        // Kiểm tra các cấu trúc response khác nhau
        const responseData = response.data as any;

        // Thử cấu trúc 1: response.data trực tiếp là array
        if (Array.isArray(response.data)) {
          console.log("[Provider] Cấu trúc 1 - response.data là array với", response.data.length, "items");
          return response.data;
        }

        // Thử cấu trúc 2: response.data.lke là array
        if (responseData && responseData.lke && Array.isArray(responseData.lke)) {
          console.log("[Provider] Cấu trúc 2 - response.data.lke là array với", responseData.lke.length, "items");
          return responseData.lke;
        }

        // Thử cấu trúc 3: response.data.data là array
        if (responseData && responseData.data && Array.isArray(responseData.data)) {
          console.log("[Provider] Cấu trúc 3 - response.data.data là array với", responseData.data.length, "items");
          return responseData.data;
        }

        console.log("[Provider] Không tìm thấy data trong bất kỳ cấu trúc nào");
        return [];
      } catch (error) {
        console.log("[Provider] layDanhSachTaiKhoanQuanLy error:", error);
        return [];
      }
    },
    [mutateUseCommonExecute.mutateAsync, listTaiKhoanDonViThuHo, profile.nsd.ma, profile.pw],
  );

  // API cập nhật danh sách tài khoản quản lý
  const capNhatDanhSachTaiKhoanQuanLy = useCallback(
    async (maTaiKhoanCha: string, danhSachMaChon: string[]) => {
      try {
        console.log("[Provider] capNhatDanhSachTaiKhoanQuanLy được gọi với:", {
          maTaiKhoanCha,
          danhSachMaChon
        });

        const response = await mutateUseCommonExecute.mutateAsync({
          ma: maTaiKhoanCha,
          a_ds_ma: danhSachMaChon,
          actionCode: ACTION_CODE.UPDATE_TAI_KHOAN_QUAN_LY_THU_HO_BHXH,
        } as any);

        console.log("[Provider] capNhatDanhSachTaiKhoanQuanLy response:", response);
        message.success("Cập nhật danh sách tài khoản quản lý thành công");
        return response.data;
      } catch (error) {
        console.log("[Provider] capNhatDanhSachTaiKhoanQuanLy error:", error);
        message.error("Có lỗi xảy ra khi cập nhật danh sách tài khoản quản lý");
        throw error;
      }
    },
    [mutateUseCommonExecute.mutateAsync],
  );

  //initData chỉ dùng trong useEffect 1 lần, không truyền đi đâu cả, nên không cần memo hoá
const initData = () => {
        getListDonVi();
    };
  const value = useMemo<IQuanLyTaiKhoanDonViThuHoContextProps>(
    () => ({
      listTaiKhoanDonViThuHo,
      listDonVi,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      filterParams,
      getListTaiKhoanDonViThuHo,
      getChiTietTaiKhoanDonViThuHo,
      capNhatChiTietTaiKhoanDonViThuHo,
      layDanhSachTaiKhoanQuanLy,
      capNhatDanhSachTaiKhoanQuanLy,
      setFilterParams,
    }),
    [listTaiKhoanDonViThuHo, listDonVi, tongSoDong, mutateUseCommonExecute.isLoading, filterParams, getListTaiKhoanDonViThuHo, getChiTietTaiKhoanDonViThuHo, capNhatChiTietTaiKhoanDonViThuHo, layDanhSachTaiKhoanQuanLy, capNhatDanhSachTaiKhoanQuanLy],
  );

  return <QuanLyTaiKhoanDonViThuHoContext.Provider value={value}>{children}</QuanLyTaiKhoanDonViThuHoContext.Provider>;
};

export default QuanLyTaiKhoanDonViThuHoProvider;
