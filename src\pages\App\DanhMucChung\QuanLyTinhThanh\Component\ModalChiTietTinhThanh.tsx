import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {FormTaoMoiTinhThanh, TRANG_THAI_TAO_MOI_TINH_THANH, DANH_SACH_MIEN_VIET_NAM,NGAY_AD_TAO_MOI_TINH_THANH} from "../index.configs";
import {useQuanLyTinhThanhContext} from "../index.context";
import {ChiTietTinhThanhProps, IModalChiTietTinhThanhRef} from "./Constant";
const {ngay_ad,ma, ten, mien, stt, trang_thai} = FormTaoMoiTinhThanh;

const ModalChiTietTinhThanhComponent = forwardRef<IModalChiTietTinhThanhRef, ChiTietTinhThanhProps>(({onAfterSave}, ref) => {
  const {listTinhThanh, capNhatChiTietTinhThanh, loading} = useQuanLyTinhThanhContext();
  
  useImperativeHandle(ref, () => ({
    open: (dataTinhThanh?: CommonExecute.Execute.IDanhMucTinhThanh) => {
      setIsOpen(true);
      if (dataTinhThanh) {
        setChiTietTinhThanh(dataTinhThanh); // Sửa dữ liệu
      } else {
        setChiTietTinhThanh(null); // Tạo mới
        form.resetFields(); // Reset form ngay khi tạo mới
      }
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietTinhThanh, setChiTietTinhThanh] = useState<CommonExecute.Execute.IDanhMucTinhThanh | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  
  // init form data - load dữ liệu vào form khi sửa
  useEffect(() => {
    if (chiTietTinhThanh && isOpen) {
      const arrFormData = [];
      for (const key in chiTietTinhThanh) {
        let value = chiTietTinhThanh[key as keyof CommonExecute.Execute.IDanhMucTinhThanh];
        
        // Convert ngay_ad từ number sang string để match với options dropdown
        if (key === 'ngay_ad' && typeof value === 'number') {
          value = String(value);
        }
        
        arrFormData.push({
          name: key,
          value: value,
        });
      }
      form.setFields(arrFormData);
    } else if (!chiTietTinhThanh && isOpen) {
      // Tạo mới - set giá trị mặc định
      form.setFields([
        { name: 'ngay_ad', value:undefined },
        { name: 'ma', value: '' },
        { name: 'ten', value: '' },
        { name: 'mien', value: undefined },
        { name: 'stt', value: '' },
        { name: 'trang_thai', value: undefined }
      ]);
    }
  }, [chiTietTinhThanh, isOpen, form]);

  //xử lý validate form
  useEffect(() => {
    if (!isOpen) return; // Chỉ validate khi modal đang mở
    
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues, isOpen]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietTinhThanh(null);
    form.resetFields();
  }, []);

  // nếu bấm lưu
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatDanhMucTinhThanhParams = form.getFieldsValue(); //lấy ra values của form
      //với trường hợp tạo mới -> check mã tỉnh thành đã tồn tại
      // if (!chiTietTinhThanh) {
      //   for (let i = 0; i < listTinhThanh.length; i++) {
      //     if (listTinhThanh[i].ma === values.ma) {
      //       form.setFields([
      //         {
      //           name: "ma",
      //           errors: ["Mã tỉnh thành đã tồn tại!"],
      //         },
      //       ]);
      //       return;
      //     }
      //   }
      // }

      await capNhatChiTietTinhThanh(values); //cập nhật lại tỉnh thành
      closeModal();
      onAfterSave?.(); // Gọi callback để refresh data
    } catch (error) {
      // Xử lý lỗi thầm lặng, đã có message error trong provider
    }
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props: IFormInput, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  
  const renderForm = () => (
    <Form form={form} layout="vertical">
      {/* NGÀY ÁP DỤNG, MÃ, TÊN */}
      <Row gutter={16}>
        {renderFormColum({...ma, disabled: chiTietTinhThanh ? true : false}, 4)}
        {renderFormColum({...ngay_ad, options: NGAY_AD_TAO_MOI_TINH_THANH,disabled: chiTietTinhThanh ? true : false}, 10)}
        {renderFormColum({...ten}, 10)}
      </Row>

      {/* MIỀN, STT, TRẠNG THÁI */}
      <Row gutter={16}>
        {renderFormColum({...mien, options: DANH_SACH_MIEN_VIET_NAM}, 8)}
        {renderFormColum({...stt}, 6)}
        {renderFormColum({...trang_thai, options: TRANG_THAI_TAO_MOI_TINH_THANH}, 10)}
      </Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietTinhThanh ? `${chiTietTinhThanh.ten}` : "Tạo mới tỉnh thành"} trang_thai_ten={chiTietTinhThanh?.trang_thai_ten} trang_thai={chiTietTinhThanh?.trang_thai} />}
        // centered
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={700}
        styles={{
          body: {
            paddingTop: "8px",
            paddingBottom: "16px",
          },
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietTinhThanhComponent.displayName = "ModalChiTietTinhThanhComponent";
export const ModalChiTietTinhThanh = memo(ModalChiTietTinhThanhComponent, isEqual);
/**
 * - Hiển thị modal để tạo mới hoặc chỉnh sửa thông tin tỉnh thành
 * - Quản lý form validation và submit
 * - Xử lý logic tạo mới vs chỉnh sửa (khác nhau ở disabled fields)
 * - Kiểm tra trùng lặp mã tỉnh thành khi tạo mới
 */