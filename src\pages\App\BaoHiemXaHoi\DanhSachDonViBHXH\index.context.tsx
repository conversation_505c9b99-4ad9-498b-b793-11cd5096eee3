import {createContext, useContext} from "react";
import {DanhSachDonViBHXHContextProps} from "./index.model";
//createContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const DanhSachDonViBHXHContext = createContext<DanhSachDonViBHXHContextProps>({
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  danhSachDonViBHXH: [],
  listDoiTac: [],
  loading: false,

  tongSoDong: 0,

  filterParams: {},
  getListDoiTac: () => Promise.resolve(),

  layDanhSachDonViBHXH: () => Promise.resolve({data: [], tong_so_dong: 0}),
  layChiTietDonViBHXH: params => Promise.resolve(null),
  setFilterParams: () => {},
  onUpdateDanhSachDonViBHXH: () => Promise.resolve(null),
});

//useContext(someContext): là React Hook cho phép bạn đọc và subscribe context từ component của mình.
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useDanhSachDonViBHXHContext = () => useContext(DanhSachDonViBHXHContext);
