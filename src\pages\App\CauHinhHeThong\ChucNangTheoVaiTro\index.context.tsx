import {createContext, useContext} from "react";
import {ChucNangTheoVaiTroContextProps} from "./index.model";
export const ChucNangTheoVaiTroContext = createContext<ChucNangTheoVaiTroContextProps>({
  loading: false,
  tongSoDong: 0,
  filterParams: {},
  danhSachChucNangTheoVaiTroPhanTrang: [],
  chiTietChucNangTheoVaiTro: {} as CommonExecute.Execute.IChiTietChucNangTheoVaiTro,
  onDeleteChucNangTheoVaiTro: () => Promise.resolve(null),
  setFilterParams: () => {},
  layDanhSachChucNangTheoVaiTroPhanTrang: () => {},
  onUpdateChucNangTheoVaiTro: () => Promise.resolve(null),
  layChiTietChucNangTheoVaiTro: () => Promise.resolve(null),
  listChucNangChuaPhanVaiTro: [],
  layDanhSachChucNangChuaPhanVaiTro: () => {},
  setListChucNangChuaPhanVaiTro: () => {},
  setChiTietChucNangTheoVaiTro: () => {},
});
export const useChucNangTheoVaiTroContext = () => useContext(ChucNangTheoVaiTroContext);
