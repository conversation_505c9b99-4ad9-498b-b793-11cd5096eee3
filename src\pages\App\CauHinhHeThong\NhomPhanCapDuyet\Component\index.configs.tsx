import {IFormInput} from "@src/@types";

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

export interface IFormChiTietNhomPhanCapDuyetFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;

  stt: IFormInput;
  trang_thai: IFormInput;
}
const FormChiTietNhomPhanCapDuyet: IFormChiTietNhomPhanCapDuyetFieldsConfig = {
  ma: {
    component: "input",
    name: "ma",
    label: "Mã nhóm phân cấp duyệt",
    placeholder: "Mã nhóm phân cấp duyệt",
    rules: [ruleRequired],
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên nhóm phân cấp duyệt",
    placeholder: "Tên nhóm phân cấp duyệt",
    rules: [ruleRequired],
  },
  stt: {
    component: "input",
    name: "stt",
    label: "Thứ tự hiển thị",
    placeholder: "Thứ tự hiển thị",
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    rules: [ruleRequired],
  },
};
export const TRANG_THAI = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

export default FormChiTietNhomPhanCapDuyet;
export interface Props {}

export interface IModalChiTietNhomPhanCapDuyetRef {
  open: (data?: CommonExecute.Execute.INhomPhanCapDuyet) => void;
  close: () => void;
}
