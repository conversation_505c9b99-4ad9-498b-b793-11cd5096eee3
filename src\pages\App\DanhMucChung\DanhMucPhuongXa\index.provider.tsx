/**
 * Tác dụng: <PERSON><PERSON>ản lý state và logic nghiệp vụ cho module danh mục phường xã
 */
import React, {memo, useCallback, useEffect, useState, useRef} from "react";
import {message} from "antd";
import {isEqual} from "lodash";

import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {useCommonExecute} from "@src/services/react-queries";
import {defaultPaginationTableProps} from "@src/hooks";

import {DanhMucPhuongXaContext} from "./index.context";
import {IDanhMucPhuongXaProvider} from "./index.model";

interface IDanhMucPhuongXaProviderProps {
  children: React.ReactNode;
}

const DanhMucPhuongXaProviderComponent: React.FC<IDanhMucPhuongXaProviderProps> = memo(({children}) => {
  //Danh sách phường xã hiển thị trong bảng chính
  const [listPhuongXa, setListPhuongXa] = useState<CommonExecute.Execute.IDanhMucPhuongXa[]>([]);
  //Danh sách tỉnh thành cho dropdown filter và form
  const [listTinhThanh, setListTinhThanh] = useState<CommonExecute.Execute.IDanhMucTinhThanh[]>([]);
  //Danh sách quận huyện cho dropdown filter và form (phụ thuộc vào tỉnh thành được chọn)
  const [listQuanHuyen, setListQuanHuyen] = useState<CommonExecute.Execute.IDanhMucQuanHuyen[]>([]);
  //Tổng số bản ghi để hiển thị phân trang
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  //Tham số filter và phân trang hiện tại
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucPhuongXaParams & ReactQuery.IPhanTrang>({
    ma_tinh: "",
    ma_quan: "",                 
    ngay_ad: "",
    ma: "",
    ten: "",
    trang_thai: "",
    trang: 1,
    so_dong: defaultPaginationTableProps.defaultPageSize,
  });

  //===== COMMON EXECUTE HOOK =====
  const {mutate: commonExecute, mutateAsync: mutateAsync, isLoading: loading} = useCommonExecute();

  //===== DEPRECATED: Giữ lại function stub để tương thích, nhưng không sử dụng =====
  const getTotalCount = useCallback(
    async (searchParams: ReactQuery.ITimKiemPhanTrangDanhMucPhuongXaParams) => {
      //Tác dụng: Workaround để tính tổng số bản ghi khi server trả về tong_so_dong = 0 nhưng vẫn có data
      try {
        const response = await mutateAsync({
          ...searchParams,
          trang: 1,
          so_dong: 1, //Chỉ lấy 1 record để tính tổng
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_PHUONG_XA,
        } as any);
        
        const responseData = response.data as any;
        const totalFromServer = responseData.tong_so_dong || 0;
        const dataArray = responseData.data || [];
        
        //Nếu server trả đúng total, sử dụng luôn
        if (totalFromServer > 0) {
          return totalFromServer;
        }
        
        //Nếu có data nhưng total = 0, estimate total bằng cách gọi với so_dong lớn
        if (dataArray.length > 0) {
          const estimateResponse = await mutateAsync({
            ...searchParams,
            trang: 1,
            so_dong: 99999, //Lấy tất cả để đếm
            actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_PHUONG_XA,
          } as any);
          
          const estimateData = (estimateResponse.data as any);
          const estimateArray = estimateData.data || [];
          return estimateArray.length;
        }
        
        return 0;
      } catch (error) {
        return 0;
      }
    },
    [mutateAsync]
  );
  
  //Tác dụng: Load tất cả tỉnh thành để hiển thị trong dropdown filter và form modal
  const getListTinhThanh = useCallback(async () => {
    try {
      const response = await mutateAsync({
        ma: "",
        ten: "",
        trang_thai: "",
        trang: 1,
        so_dong: 100,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_TINH_THANH
      }as any);
      
      const responseData = (response.data as any);
      setListTinhThanh(responseData.data || []);
    } catch (error) {
      setListTinhThanh([]);
    }
  }, [mutateAsync]);

  //Tác dụng: Load quận huyện theo tỉnh thành được chọn để hiển thị trong dropdown
  const getListQuanHuyen = useCallback(async (params?: {ma_tinh?: string}) => {
    try {
      const response = await mutateAsync({
        ma: "",
        ten: "",
        trang_thai: "",
        ma_tinh: params?.ma_tinh || "",
        trang: 1,
        so_dong: 1000,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_QUAN_HUYEN
      }as any);
      
      const responseData = (response.data as any);
      setListQuanHuyen(responseData.data || []);
    } catch (error) {
      setListQuanHuyen([]);
    }
  }, [mutateAsync]);

  //Tác dụng: Load danh sách phường xã theo điều kiện filter và phân trang để hiển thị trong bảng
  const getListPhuongXa = useCallback(
    async (params?: ReactQuery.ITimKiemPhanTrangDanhMucPhuongXaParams & ReactQuery.IPhanTrang) => {
      try {
        const searchParams = params || filterParams;

        commonExecute(
          {
            ...(searchParams as any),
            actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_PHUONG_XA,
          },
          {
            onSuccess: async (response: any) => {
              const responseData = response.data;
              const dataArray = responseData.data || [];
              const totalRecords = responseData.tong_so_dong || 0;
              
              //Xử lý trường hợp server trả tong_so_dong = 0 nhưng vẫn có data (bug server)
              if (totalRecords === 0 && dataArray.length > 0) {
                try {
                  const actualTotal = await getTotalCount({
                    ma_tinh: searchParams.ma_tinh || "",
                    ma_quan: searchParams.ma_quan || "",
                    ngay_ad: searchParams.ngay_ad || "",
                    ma: searchParams.ma || "",
                    ten: searchParams.ten || "",
                    trang_thai: searchParams.trang_thai || "",
                  });
                  setTongSoDong(actualTotal);
                } catch (error) {
                  setTongSoDong(dataArray.length);
                }
              } else {
                //Sử dụng trực tiếp tong_so_dong từ server, fallback về dataArray.length nếu cần
                setTongSoDong(totalRecords || dataArray.length);
              }
              
              setListPhuongXa(dataArray);
            },
            onError: (error: any) => {
              message.error("Có lỗi xảy ra khi tải danh sách phường xã!");
            },
          }
        );
      } catch (error) {
        //Xử lý lỗi thầm lặng
      }
    },
    [commonExecute, filterParams, getTotalCount]
  );

  //Tác dụng: Lấy thông tin chi tiết của 1 phường xã khi user click vào row để hiển thị trong modal edit
  const getChiTietPhuongXa = useCallback(
    async (data: {ma_tinh: string; ma_quan: string; ma: string; ngay_ad: number}) => { 
      try {
        const response = await mutateAsync({
          ma_tinh: data.ma_tinh,
          ma_quan: data.ma_quan,     
          ma: data.ma,
          ngay_ad: data.ngay_ad,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_PHUONG_XA,
        } as any);
        
        //Response structure confirmed: data.lke[0]
        const responseData = response.data as any;
        
        if (responseData?.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          return responseData.lke[0] as CommonExecute.Execute.IDanhMucPhuongXa;
        }
        
        return {} as CommonExecute.Execute.IDanhMucPhuongXa;
      } catch (error) {
        message.error("Không thể tải chi tiết phường xã");
        return {} as CommonExecute.Execute.IDanhMucPhuongXa;
      }
    },
    [mutateAsync],
  );

  //Tác dụng: Lưu thông tin phường xã mới hoặc cập nhật thông tin phường xã đã có
  const capNhatChiTietPhuongXa = useCallback(
    async (params: ReactQuery.ICapNhatDanhMucPhuongXaParams, isUpdate: boolean = false): Promise<any> => {
      try {
        const response = await mutateAsync({
          ...params,
          actionCode: ACTION_CODE.UPDATE_DANH_MUC_PHUONG_XA,
        } as any);
        
        //API trả về -1 là thành công
        if (response?.data === -1) {
          const successMessage = isUpdate ? "Cập nhật phường xã thành công!" : "Thêm mới phường xã thành công!";
          message.success(successMessage);
          return response.data;
        } else {
          const errorMessage = isUpdate ? "Có lỗi xảy ra khi cập nhật phường xã!" : "Có lỗi xảy ra khi thêm mới phường xã!";
          message.error(errorMessage);
          throw new Error(`API returned failure. Response data: ${response?.data}`);
        }
      } catch (error) {
        const errorMessage = isUpdate ? "Có lỗi xảy ra khi cập nhật phường xã!" : "Có lỗi xảy ra khi thêm mới phường xã!";
        message.error(errorMessage);
        throw error;
      }
    },
    [mutateAsync]
  );

  //===== EFFECTS =====
  //Load danh sách tỉnh thành khi component mount
  useEffect(() => {
    getListTinhThanh();
    //Load tất cả quận huyện ban đầu (không filter theo tỉnh)
    getListQuanHuyen();
  }, [getListTinhThanh, getListQuanHuyen]);

  //Load dữ liệu phường xã khi filterParams thay đổi
  useEffect(() => {
    getListPhuongXa();
  }, [filterParams, getListPhuongXa]);

  //===== CONTEXT VALUE =====
  const contextValue: IDanhMucPhuongXaProvider = {
    listPhuongXa,
    listTinhThanh,
    listQuanHuyen,
    tongSoDong,
    loading,
    filterParams,
    getListPhuongXa,
    getListTinhThanh,
    getListQuanHuyen,
    getChiTietPhuongXa,
    capNhatChiTietPhuongXa,
    setFilterParams,
    getTotalCount,
  };

  return <DanhMucPhuongXaContext.Provider value={contextValue}>{children}</DanhMucPhuongXaContext.Provider>;
});

DanhMucPhuongXaProviderComponent.displayName = "DanhMucPhuongXaProvider";
export default DanhMucPhuongXaProviderComponent;
