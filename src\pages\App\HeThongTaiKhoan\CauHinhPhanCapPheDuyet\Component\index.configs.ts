import {IFormInput} from "@src/@types";
import {REGUlAR_EXPRESSION} from "@src/constants";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";

import {formatCurrencyUS, parseDateTime} from "@src/utils";
import {TableProps} from "antd";

export const initFormFields = (form: any, chiTietNguoiSuDung: any) => {
  if (!chiTietNguoiSuDung) return;

  const DATE_TIME_FIELDS = ["ngay_hl", "ngay_kt"];
  const fields = Object.entries(chiTietNguoiSuDung || {}).map(([name, value]) => {
    if (!value) return {name, value: ""};
    //formart ngày
    if (DATE_TIME_FIELDS.includes(name)) {
      return {
        name,
        value: parseDateTime(value),
        errors: [],
      };
    }

    return {name, value, errors: []};
  });
  form.setFields(fields);
};

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};
export interface IFormChiTietCauHinhPhanCapPheDuyetFieldsConfig {
  ma_doi_tac: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  phong: IFormInput;
  password: IFormInput;
  dthoai: IFormInput;
  ngay_hl: IFormInput;
  ngay_kt: IFormInput;
  ma_chi_nhanh: IFormInput;
  ma_chuc_danh: IFormInput;
  email: IFormInput;
  trang_thai: IFormInput;
}
// const FormChiTietDanhMucSanPham: IFormChiTietDanhMucSanPhamFieldsConfig = {
export const FormChiTietCauHinhPhanCapPheDuyet: IFormChiTietCauHinhPhanCapPheDuyetFieldsConfig = {
  ten: {
    component: "input",
    name: "ten",
    placeholder: "Tên người dùng",
    label: "Tên người dùng", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
  dthoai: {
    component: "input",
    name: "dthoai",
    placeholder: "Điện thoại",
    label: "Điện thoại", // cho label vào thì sẽ thành input with label
    rules: [
      ruleRequired,
      {
        pattern: REGUlAR_EXPRESSION.REG_PHONE,
        message: "Số điện thoại sai định dạng",
      },
    ],
  },
  email: {
    component: "input",
    name: "email",
    placeholder: "Email",
    label: "Email", // cho label vào thì sẽ thành input with label
    rules: [
      ruleRequired,
      {
        pattern: REGUlAR_EXPRESSION.REG_EMAIL,
        message: "Email sai định dạng",
      },
    ],
  },
  ma: {
    component: "input",
    name: "ma",
    placeholder: "Nhập mã người dùng",
    label: "Mã người dùng", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
  password: {
    component: "input",
    name: "password",
    placeholder: "Mật khẩu",
    label: "Mật khẩu", // cho label vào thì sẽ thành input with label
  },
  ma_chuc_danh: {
    component: "select",
    name: "ma_chuc_danh",
    placeholder: "Chức danh",
    label: "Chức danh", // cho label vào thì sẽ thành input with label
  },
  phong: {
    component: "select",
    name: "phong",
    placeholder: "Chọn phòng ban",
    label: "Phòng ban", // cho label vào thì sẽ thành input with label
  },
  ma_chi_nhanh: {
    component: "select",
    name: "ma_chi_nhanh",
    placeholder: "Chọn chi nhánh",
    label: "Chi nhánh", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
  ngay_hl: {
    component: "date-picker",
    name: "ngay_hl",
    placeholder: "Ngày hiệu lực",
    label: "Ngày hiệu lực",
    rules: [ruleRequired],
  },
  ngay_kt: {
    component: "date-picker",
    name: "ngay_kt",
    placeholder: "Ngày hiệu lực",
    label: "Ngày hiệu lực",
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    placeholder: "Trạng thái",
    label: "Trạng thái ",
    rules: [ruleRequired],
  },
  ma_doi_tac: {
    component: "select",
    name: "ma_doi_tac",
    placeholder: "Đối tác",
    label: "Đối tác ",
    rules: [ruleRequired],
  },
};
export const TRANG_THAI_NGUOI_DUNG = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngừng sử dụng"},
];
//Bảng cấu hình phân cấp phê duyệt ngày ad
export interface TableCauHinhPhanCapPheDuyetDataType {
  key: string;
  ngay_ad: string;
  bt: number;
  ngay_cap_nhat: string;
  nguoi_cap_nhat: string;
  hanh_dong?: () => JSX.Element | null; // Định nghĩa hanh_dong là hàm trả về JSX.Element;
}
export const cauHinhPhanCapPheDuyetColumns: TableProps<TableCauHinhPhanCapPheDuyetDataType>["columns"] = [
  {
    title: "Ngày áp dụng",
    dataIndex: "ngay_ad",
    key: "ngay_ad",
    // width: 150,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, ...defaultTableColumnsProps},
  {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, ...defaultTableColumnsProps},
  {
    title: "Xóa",
    dataIndex: "hanh_dong",
    // key: "hanh_dong",
    width: 50,

    render: (_, record) => record.hanh_dong?.(),
    ...defaultTableColumnsProps,
  },
];
export type DataIndexCauHinhPhanCapPheDuyet = keyof TableCauHinhPhanCapPheDuyetDataType;
//bảng cấu hình phân cấp phê duyệt chi tiết
export interface TableCauHinhPhanCapPheDuyetChiTietDataType {
  key: string;
  stt: string;
  nhom_duyet?: string;
  ma_doi_tac_ql?: string;
  ma_chi_nhanh_ql?: string;
  chi_nhanh_ql_ten_tat?: string;
  doi_tac_ql_ten_tat?: string;
  ten_sp?: string;
  ten_nhom?: string;
  nv?: string;
  ma_sp?: string;
  tien_tu?: number;
  tien_toi?: number;
  bt_phan_cap?: number;
  bt?: number;
  hanh_dong?: () => JSX.Element | null; // Định nghĩa hanh_dong là hàm trả về JSX.Element;
}
export const cauHinhPhanCapPheDuyetChiTietColumns: TableProps<TableCauHinhPhanCapPheDuyetChiTietDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "stt",
    key: "stt",
    width: colWidthByKey.sott,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Nhóm",
    dataIndex: "ten_nhom",
    key: "ten_nhom",
    // width: 120,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã đối tác quản lý",
    dataIndex: "doi_tac_ql_ten_tat",
    key: "doi_tac_ql_ten_tat",
    width: 150,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã chi nhánh quản lý",
    dataIndex: "chi_nhanh_ql_ten_tat",
    key: "chi_nhanh_ql_ten_tat",
    width: 150,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Nghiệp vụ",
    dataIndex: "nv",
    key: "nv",
    width: 100,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Sản phẩm",
    dataIndex: "ten_sp",
    key: "ten_sp",
    width: 150,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    ...defaultTableColumnsProps,
    title: "Tiền từ",
    dataIndex: "tien_tu",
    key: "tien_tu",
    width: 120,
    align: "right",
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Tiền tới",
    dataIndex: "tien_toi",
    key: "tien_toi",
    width: 120,
    align: "right",
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
    },
  },
  {
    title: "Xóa",
    dataIndex: "hanh_dong",
    // key: "hanh_dong",
    width: 50,

    render: (_, record) => record.hanh_dong?.(),
    ...defaultTableColumnsProps,
  },
];
export type DataIndexCauHinhPhanCapPheDuyetCT = keyof TableCauHinhPhanCapPheDuyetChiTietDataType;
export interface IFormThemNgayApDungFieldsConfig {
  ngay_ad?: IFormInput;
}

export const FormThemNgayApDung: IFormThemNgayApDungFieldsConfig = {
  ngay_ad: {
    component: "date-picker",
    name: "ngay_ad",
    placeholder: "Ngày áp dụng",
    label: "Ngày áp dụng", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
};
//form thêm chi tiết phân cấp phê duyệt
export interface IFormThemPhanCapPheDuyetCTFieldsConfig {
  bt_cap_nhat?: IFormInput;
  bt?: IFormInput;
  nhom_duyet?: IFormInput;
  ma_doi_tac_ql?: IFormInput;
  ma_chi_nhanh_ql?: IFormInput;
  // chi_nhanh_ql_ten_tat?: IFormInput;
  // doi_tac_ql_ten_tat?: IFormInput;
  // ten_sp?: IFormInput;
  // ten_nhom?: IFormInput;
  nv?: IFormInput;
  ma_sp?: IFormInput;
  tien_tu?: IFormInput;
  tien_toi?: IFormInput;
  trang_thai?: IFormInput;
}

export const FormThemPhanCapPheDuyetCT: IFormThemPhanCapPheDuyetCTFieldsConfig = {
  // bt_cap_nhat: {
  //   component: "date-picker",
  //   name: "ngay_ad",
  //   placeholder: "Ngày áp dụng",
  //   label: "Ngày áp dụng",
  // },
  // bt: {
  //   component: "date-picker",
  //   name: "ngay_ad",
  //   placeholder: "Ngày áp dụng",
  //   label: "Ngày áp dụng",
  // },
  nhom_duyet: {
    component: "select",
    name: "nhom_duyet",
    placeholder: "Nhóm duyệt",
    label: "Nhóm duyệt",
    rules: [ruleRequired],
  },
  ma_doi_tac_ql: {
    component: "select",
    name: "ma_doi_tac_ql",
    placeholder: "Đối tác quản lý",
    label: "Mã đối tác quản lý",
    rules: [ruleRequired],
  },
  ma_chi_nhanh_ql: {
    component: "select",
    name: "ma_chi_nhanh_ql",
    placeholder: "Chi nhánh quản lý",
    label: "Mã chi nhánh quản lý",
    rules: [ruleRequired],
  },
  nv: {
    component: "select",
    name: "nv",
    placeholder: "Nghiệp vụ",
    label: "Nghiệp vụ",
    rules: [ruleRequired],
  },
  ma_sp: {
    component: "select",
    name: "ma_sp",
    placeholder: "Mã sản phẩm",
    label: "Mã sản phẩm",
    rules: [ruleRequired],
  },
  tien_tu: {
    component: "input-price",
    name: "tien_tu",
    placeholder: "0",
    label: "Tiền từ",
    // textAlign: "left",
    rules: [ruleRequired],
  },
  tien_toi: {
    component: "input-price",
    name: "tien_toi",
    placeholder: "0",
    label: "Tiền tới",
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    placeholder: "Trạng thái",
    label: "Trạng thái",
  },
};
export interface IModalThemCauHinhPhanCapPheDuyetRef {
  open: (data?: CommonExecute.Execute.ICauHinhPhanCapPheDuyetCT) => void;
  close: () => void;
}
export interface Props {
  PhanCapPheDuyet: number | null;
}
export const NHOM_DUYET = [
  {ten: "ADMIN", ma: "ADMIN"},
  {ten: "CLIENT", ma: "CLIENT"},
];
export const TRANG_THAI_PHAN_CAP_PHE_DUYET_CT = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];
export const NGHIEP_VU_PHAN_CAP_PHE_DUYET_CT = [
  {ten: "Bảo hiểm con người", ma: "NG"},
  {ten: "Bảo hiểm xe cơ giới", ma: "XCG"},
  {ten: "Bảo hiểm tài sản", ma: "TS"},
];
