import {CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal} from "@src/components";
import {usePhongBan} from "@src/hooks";

import {Col, Form, Modal, Row, Tabs} from "antd";
import {forwardRef, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {useQuanLyTaiKhoanNguoiDungContext} from "../index.context";
import {FormChiTietTaiKhoanNguoiDung, initFormFields, TRANG_THAI_NGUOI_DUNG} from "./index.configs";
import TabPhanQuyenChucNangTheoVaiTro from "./TabPhanQuyenChucNangTheoVaiTro";
import TabPhanQuyenChucNangTheoNhom from "./TabPhanQuyenChucNangTheoNhom";
import TabDonViQuanLy from "./TabDonViQuanLy";
import dayjs from "dayjs";

interface Props {
  danhSachTaiKhoanNguoiDung: Array<CommonExecute.Execute.IDanhSachTaiKhoanNguoiDungPhanTrang>;
}

export interface IModalChiTietTaiKhoanNguoiDungRef {
  open: (data?: CommonExecute.Execute.IChiTietNguoiSuDung) => void;
  close: () => void;
}

const ModalChiTietTaiKhoanNguoiDung = forwardRef<IModalChiTietTaiKhoanNguoiDungRef, Props>(({danhSachTaiKhoanNguoiDung}: Props, ref) => {
  const listPhongBan = usePhongBan();

  const {onUpdateTaiKhoanNguoiDung, listChucDanh, listDoiTac, getListChiNhanhTheoDoiTac, listChiNhanh, chucNangSelected, donViSelected, menuSelected} = useQuanLyTaiKhoanNguoiDungContext();

  const {dthoai, ten, email, password, ma_chuc_danh, phong, ma_chi_nhanh, ma, ma_doi_tac, ngay_hl, ngay_kt, trang_thai} = FormChiTietTaiKhoanNguoiDung;
  const [formChiTietTaiKhoanNguoiDung] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const formValues = Form.useWatch([], formChiTietTaiKhoanNguoiDung);
  const [chiTietNguoiSuDung, setChiTietNguoiSuDung] = useState<CommonExecute.Execute.IChiTietNguoiSuDung | null>(null);
  const [filteredPhongBan, setFilteredPhongBan] = useState<Array<CommonExecute.Execute.IDanhMucPhongBan>>([]);
  const [activeTab, setActiveTab] = useState("1");
  const [filteredChiNhanh, setFilteredChiNhanh] = useState<Array<CommonExecute.Execute.IChiNhanh>>([]);
  // const [selectedDonViQuanLy, setSelectedDonViQuanLy] = useState<TableDonViQuanLyDataType[]>([]);
  // const [selectedMenu, setSelectedMenu] = useState<TableMenuNguoiDungDataType[]>([]);

  // Hàm callback nhận dữ liệu từ TabDonViQuanLy
  const closeModal = useCallback(() => {
    setIsOpen(false);

    // setchitietMenu(null);
    // formThemMenuCha.resetFields();
    // setFilterParams({...filterParams});
    // console.log("filterparamss", filterParams);
  }, []);
  useImperativeHandle(ref, () => ({
    open: (dataChiTietNguoiSuDung?: CommonExecute.Execute.IChiTietNguoiSuDung) => {
      setIsOpen(true);
      setDisableSubmit(true);
      if (dataChiTietNguoiSuDung) setChiTietNguoiSuDung(dataChiTietNguoiSuDung);
    },
    close: () => setIsOpen(false),
  }));
  // init form data gọi vào index.configs
  useEffect(() => {
    // console.log("chi tiết người sửa dung", chiTietNguoiSuDung);

    initFormFields(formChiTietTaiKhoanNguoiDung, chiTietNguoiSuDung);
  }, [chiTietNguoiSuDung]);

  // Lọc danh sách phòng ban theo mã chi nhánh
  useEffect(() => {
    if (chiTietNguoiSuDung?.ma_chi_nhanh_ql) {
      const filtered = listPhongBan.listPhongBan.filter(pb => pb.ma_chi_nhanh === chiTietNguoiSuDung.ma_chi_nhanh_ql);
      setFilteredPhongBan(filtered);
    } else {
      setFilteredPhongBan([]);
    }
  }, [chiTietNguoiSuDung?.ma_chi_nhanh_ql, listPhongBan.listPhongBan]);
  useEffect(() => {
    if (isOpen) {
      setActiveTab("1");
    }
  }, [isOpen]);
  //xử lý validate form
  useEffect(() => {
    formChiTietTaiKhoanNguoiDung
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [formChiTietTaiKhoanNguoiDung, formValues]);

  //Bấm Update
  const onPressUpdateTaiKhoanNguoiDung = async () => {
    try {
      const values: ReactQuery.IUpdateTaiKhoanNguoiDungParams = formChiTietTaiKhoanNguoiDung.getFieldsValue(); //lấy ra values của form
      const param: ReactQuery.IUpdateTaiKhoanNguoiDungParams = {
        ...values,
        ngay_hl: dayjs(values.ngay_hl).format("YYYYMMDD"),
        ngay_kt: dayjs(values.ngay_hl).format("YYYYMMDD"),
        qly: donViSelected.map(row => ({
          ma_doi_tac_ql: row.ma_doi_tac_ql,
          ma_chi_nhanh_ql: row.ma_chi_nhanh_ql,
        })),
        menu: menuSelected.map(row => ({
          ma: row.ma,
        })),
        quyen: chucNangSelected.map(row => ({
          ma: row.ma_chuc_nang,
        })),
      };
      const response = await onUpdateTaiKhoanNguoiDung(param);

      // if (response === -1) {
      //   console.log("cập nhật thành công");
      //   closeModal();
      // } else {
      //   console.log("cập nhật thất bại");
      // }
      // const dataChiTiet = await layChiTietTaiKhoanNguoiDung(values);
      // setChiTietNguoiSuDung(dataChiTiet);
    } catch (error: any) {
      console.log("onConfirm", error);
    }
  };

  const handleChangeDoiTac = (maDoiTac: string) => {
    console.log("hàm chang đối tác");
    getListChiNhanhTheoDoiTac();
    const filtered = listChiNhanh.filter(pb => pb.ma_doi_tac === maDoiTac);
    setFilteredChiNhanh(filtered);
  };
  const optionsChucDanh = listChucDanh.map((item, idx) => ({
    ...item,
    value: item.ma,
    label: item.label || item.ten,
  }));

  // Xử lý khi chọn chi nhánh
  const handleChangeChiNhanh = (maChiNhanh: string) => {
    // Lọc danh sách phòng ban theo mã chi nhánh
    const filtered = listPhongBan.listPhongBan.filter(pb => pb.ma_chi_nhanh === maChiNhanh);
    setFilteredPhongBan(filtered);
    // Lấy giá trị phòng ban hiện tại từ chiTietNguoiSuDung
    const currentPhong = chiTietNguoiSuDung?.phong;
    // Kiểm tra phòng ban hiện tại có trong filtered không
    const found = filtered.find(pb => pb.value === currentPhong);
    if (found) {
      formChiTietTaiKhoanNguoiDung.setFieldsValue({phong: currentPhong}); // Nếu có thì set lại giá trị
    } else {
      formChiTietTaiKhoanNguoiDung.setFieldsValue({phong: null}); // Nếu không có thì reset
    }
  };
  const handleTabChange = async (activeKey: string) => {
    setActiveTab(activeKey);
  };
  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="primary" htmlType="submit" form="formUpdatePhongBan" disabled={disableSubmit} onClick={onPressUpdateTaiKhoanNguoiDung} className="mr-2 w-40" icon={<CheckOutlined />}>
          Lưu
        </Button>
        {/* <Popcomfirm
          title="Thông báo"
          onConfirm={onPressUpdateTaiKhoanNguoiDung}
          htmlType="submit"
          okText="Lưu"
          description="Bạn có chắc chắn muốn lưu thông tin?"
          buttonTitle={"   Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        /> */}
      </Form.Item>
    );
  };
  //Render

  const renderFormInputColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderTabs = () => {
    const isEdit = !!chiTietNguoiSuDung;

    return (
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <Tabs.TabPane key="1" tab="Đơn vị quản lý / Menu">
          <TabDonViQuanLy
            // filterValues={filterValues}
            chiTietNguoiSuDung={chiTietNguoiSuDung}
            // onDataChange={handleDonViQuanLyChange} // Truyền callback cho đơn vị quản lý
            // onDataChangeMenu={handleMenuChange}
          />
        </Tabs.TabPane>
        <Tabs.TabPane key="2" tab="Phân quyền chức năng theo vai trò">
          <TabPhanQuyenChucNangTheoVaiTro
            // filterValues={filterValues}
            chiTietNguoiSuDung={chiTietNguoiSuDung}
          />
        </Tabs.TabPane>
        <Tabs.TabPane key="3" tab="Phân quyền chức năng theo nhóm">
          <TabPhanQuyenChucNangTheoNhom
            // filterValues={filterValues}
            chiTietNguoiSuDung={chiTietNguoiSuDung}
          />
        </Tabs.TabPane>
      </Tabs>
    );
  };
  return (
    <Modal
      className="modal-chi-tiet-tai-khoan"
      title={
        <HeaderModal
          title={chiTietNguoiSuDung ? `Thông tin người dùng ${chiTietNguoiSuDung.ten}` : "Thêm người sử dụng"}
          trang_thai_ten={chiTietNguoiSuDung?.trang_thai_ten}
          trang_thai={chiTietNguoiSuDung?.trang_thai}
        />
      }
      centered
      open={isOpen}
      onOk={() => setIsOpen(false)}
      onCancel={() => {
        formChiTietTaiKhoanNguoiDung.resetFields();
        setIsOpen(false);
        setChiTietNguoiSuDung(null);
      }}
      footer={renderFooter}
      closable
      maskClosable={false}
      width="100vw"
      style={{
        top: 0,
        left: 0,
        padding: 0,
      }}
      styles={{
        body: {
          height: "76vh",
        },
      }}
      // className="custom-full-modal m-2"
    >
      <Form id="formUpdateTaiKhoanNguoiDung" onFinish={onPressUpdateTaiKhoanNguoiDung} form={formChiTietTaiKhoanNguoiDung} layout="vertical" autoComplete="on">
        <Row gutter={16}>
          {renderFormInputColum({...ma_doi_tac, options: listDoiTac, onChange: (value: string) => handleChangeDoiTac(value), disabled: chiTietNguoiSuDung ? true : false})}
          {renderFormInputColum({...ma_chi_nhanh, options: filteredChiNhanh, disabled: chiTietNguoiSuDung ? true : false, onChange: handleChangeChiNhanh})}
          {renderFormInputColum({...phong, options: filteredPhongBan, disabled: chiTietNguoiSuDung ? true : false, onChange: () => {}})}
          {renderFormInputColum({...ma, disabled: chiTietNguoiSuDung ? true : false})}
          {renderFormInputColum(ten)}
          {renderFormInputColum({...password})}
          {renderFormInputColum({...ngay_hl})}
          {renderFormInputColum({...ngay_kt})}
          {renderFormInputColum({...ma_chuc_danh, options: optionsChucDanh, onChange: () => {}})}
          {renderFormInputColum(dthoai)}
          {renderFormInputColum(email)}
          {renderFormInputColum({...trang_thai, options: TRANG_THAI_NGUOI_DUNG})}
        </Row>
      </Form>
      {renderTabs()}
    </Modal>
  );
});
ModalChiTietTaiKhoanNguoiDung.displayName = "ModalChiTietTaiKhoanNguoiDung";
export default ModalChiTietTaiKhoanNguoiDung;
// ModalChiTietTaiKhoanNguoiDungComponent.displayName = "ModalChiTietTaiKhoanNguoiDungComponent";
// export const ModalChiTietTaiKhoanNguoiDung = memo(ModalChiTietTaiKhoanNguoiDungComponent, isEqual);
