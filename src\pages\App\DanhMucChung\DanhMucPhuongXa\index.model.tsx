import {ReactQuery} from "@src/@types";

//Interface định nghĩa contract cho DanhMucPhuongXaProvider
export interface IDanhMucPhuongXaProvider {
  // Dữ liệu hiển thị
  listPhuongXa: CommonExecute.Execute.IDanhMucPhuongXa[]; // Danh sách phường xã hiển thị trong bảng
  listTinhThanh: CommonExecute.Execute.IDanhMucTinhThanh[]; // Danh sách tỉnh thành cho dropdown
  listQuanHuyen: CommonExecute.Execute.IDanhMucQuanHuyen[]; // Danh sách quận huyện cho dropdown
  tongSoDong: number; // Tổng số bản ghi để hiển thị pagination
  loading: boolean; // Trạng thái loading cho UI

  // Tham số filter và phân trang
  filterParams: ReactQuery.ITimKiemPhanTrangDanhMucPhuongXaParams & ReactQuery.IPhanTrang; // Tham số filter và phân trang

  // Actions
  getListPhuongXa: (params?: ReactQuery.ITimKiemPhanTrangDanhMucPhuongXaParams & ReactQuery.IPhanTrang) => Promise<void>; // Tìm kiếm danh sách phường xã
  getListTinhThanh: () => Promise<void>; // Load danh sách tỉnh thành
  getListQuanHuyen: (params?: {ma_tinh?: string}) => Promise<void>; // Load danh sách quận huyện theo tỉnh thành
  getChiTietPhuongXa: (data: {ma_tinh: string; ma_quan: string; ma: string; ngay_ad: number}) => Promise<CommonExecute.Execute.IDanhMucPhuongXa>; // Lấy chi tiết phường xã
  capNhatChiTietPhuongXa: (data: ReactQuery.ICapNhatDanhMucPhuongXaParams, isUpdate?: boolean) => Promise<any>; // Tạo mới hoặc cập nhật phường xã
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhMucPhuongXaParams & ReactQuery.IPhanTrang>>; // Cập nhật tham số filter
  getTotalCount: (searchParams: ReactQuery.ITimKiemPhanTrangDanhMucPhuongXaParams) => Promise<number>; // Workaround để lấy tổng số bản ghi khi server trả về sai
}
