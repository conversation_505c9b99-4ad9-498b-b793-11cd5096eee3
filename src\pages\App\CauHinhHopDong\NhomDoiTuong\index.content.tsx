import {ClearOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import type {FilterDropdownProps} from "antd/es/table/interface";
import React, {memo, useCallback, useEffect, useMemo, useRef, useState} from "react";

import {ModalChiTietNhomDoiTuong} from "./Component/ModalChiTietNhomDoiTuong";
import {FormTimKiemNhomDoiTuong, TRANG_THAI, TableNhomDoiTuongDataType, danhMucNhomDoiTuongColumns, radioItemTrangThaiNhomDoiTuongTable} from "./index.configs";
import {useNhomDoiTuongContext} from "./index.context"; // file này lưu biến về state
// import "./index.dark.scss";
import "./index.default.scss";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {IModalChiTietNhomDoiTuongRef} from "./Component/index.configs";

type DataIndex = keyof TableNhomDoiTuongDataType;
/*
file LoginContent : xử lý phần UI của màn hình
React.Fc : khai báo rằng type LoginContent là 1 React Function
*/
// const Highlighter = Highlighter as unknown as React.FC<any>; //tạm thời vượt qua lỗi 'Highlighter' cannot be used as a JSX component...

const NhomDoiTuongContent: React.FC = () => {
  useEffect(() => {
    onSearchApi({
      ...searchParams,
      trang: page,
      so_dong: pageSize,
    });
  }, []);
  // const {listDoiTac} = useDoiTac();
  const [danhSachNhomDoiTuong, setDanhSachNhomDoiTuong] = useState<Array<CommonExecute.Execute.INhomDoiTuong>>([]);
  const [tongSoDongNhomDoiTuong, setTongSoDongNhomDoiTuong] = useState<number>(0);
  const {loading, layDanhSachNhomDoiTuongPhanTrang, tongSoDong, layChiTietNhomDoiTuong, filterParams, setFilterParams, listDoiTac} = useNhomDoiTuongContext();
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangNhomDoiTuongParams>(filterParams);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPaginationTableProps.defaultPageSize);

  const refModalChiTietNhomDoiTuong = useRef<IModalChiTietNhomDoiTuongRef>(null);
  const {ma, ten, trang_thai} = FormTimKiemNhomDoiTuong;

  const dataTableListNhomDoiTuong = useMemo<Array<TableNhomDoiTuongDataType>>(() => {
    try {
      const tableData = danhSachNhomDoiTuong.map((item: any, index: number) => {
        return {
          key: index.toString(),
          // stt: item.stt ?? index + 1,
          sott: item.sott,
          ma: item.ma,
          ten: item.ten,
          ma_cha: item.ma_cha,
          ma_dau: item.ma_dau,
          mo_ta: item.mo_ta,
          stt: item.stt,
          ngay_tao: item.ngay_tao,
          nguoi_tao: item.nguoi_tao,
          ngay_cap_nhat: item.ngay_cap_nhat,
          nguoi_cap_nhat: item.nguoi_cap_nhat,
          trang_thai: item.trang_thai,
          trang_thai_ten: item.trang_thai_ten,
        };
      });
      const arrEmptyRow: Array<TableNhomDoiTuongDataType> = fillRowTableEmpty(tableData.length, defaultPaginationTableProps.defaultPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListNhomDoiTuong error", error);
      return [];
    }
  }, [danhSachNhomDoiTuong]);
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  //Bấm tìm kiếm
  const onSearchApi = async (values: ReactQuery.ITimKiemPhanTrangNhomDoiTuongParams & ReactQuery.IPhanTrang) => {
    try {
      const cleanedValues = {
        ...values,
        ma: values.ma ?? "",
        ten: values.ten ?? "",
        trang_thai: values.trang_thai ?? "",
        trang: values.trang ?? 1,
        so_dong: values.so_dong,
      };
      setSearchParams(cleanedValues);
      setPage(1); // Reset về trang 1 khi search mới

      const result = await layDanhSachNhomDoiTuongPhanTrang({...cleanedValues, trang: 1, so_dong: pageSize});
      console.log("result", result);
      if (result) {
        setDanhSachNhomDoiTuong(result.data);
        setTongSoDongNhomDoiTuong(result.tong_so_dong);
      } else {
        setDanhSachNhomDoiTuong([]);
        setTongSoDongNhomDoiTuong(0);
      }
    } catch (error) {
      console.log("onSearchApi error:", error);
      setDanhSachNhomDoiTuong([]);
      setTongSoDongNhomDoiTuong(0);
    }
  };

  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    async (page: number, pageSize: number) => {
      try {
        setPage(page);
        setPageSize(pageSize);

        const result = await layDanhSachNhomDoiTuongPhanTrang({...searchParams, trang: page, so_dong: pageSize});
        if (result) {
          setDanhSachNhomDoiTuong(result.data);
          setTongSoDongNhomDoiTuong(result.tong_so_dong);
        } else {
          setDanhSachNhomDoiTuong([]);
          setTongSoDongNhomDoiTuong(0);
        }
      } catch (error) {
        console.log("onChangePage error:", error);
        setDanhSachNhomDoiTuong([]);
        setTongSoDongNhomDoiTuong(0);
      }
    },
    [searchParams, pageSize, layDanhSachNhomDoiTuongPhanTrang],
  );
  //Đồng bộ chức năng
  // RENDER
  //tạo cột tìm kiếm
  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TableNhomDoiTuongDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiNhomDoiTuongTable : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      ); // xử lý chuyển text thành 1 dòng khi text quá dài;
    },
  });

  const renderFormInputColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  //render header table
  const renderHeaderTableNhomDoiTuong = () => {
    return (
      <div>
        <Form initialValues={{trang_thai: TRANG_THAI[0].ma}} layout="vertical" className="[&_.ant-form-item]:!mb-0" onFinish={onSearchApi}>
          <div className="flex flex-col gap-5">
            <Row gutter={16} align={"bottom"}>
              {renderFormInputColum({...ma})}
              {/* {renderFormInput(ma)} */}
              {renderFormInputColum(ten)}
              {renderFormInputColum({...trang_thai, options: TRANG_THAI})}
              <Col span={2}>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
                  Tìm kiếm
                </Button>
              </Col>
              <Col span={2}>
                <Button type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalChiTietNhomDoiTuong.current?.open()} loading={loading} block>
                  Tạo mới
                </Button>
              </Col>
            </Row>
            {/* {renderFormSelect(ma_doi_tac_ql, listDoiTac)} */}
          </div>
        </Form>
      </div>
    );
  };

  //render table
  return (
    <div id={ID_PAGE.DANH_MUC_DAI_LY} className="[&_.ant-space]:w-full">
      <Table<TableNhomDoiTuongDataType>
        {...defaultTableProps}
        loading={loading}
        onRow={(record, rowIndex) => {
          return {
            onClick: async event => {
              if (record.key.toString().includes("empty")) return;
              const chiTietNhomDoiTuong = await layChiTietNhomDoiTuong({ma: record.ma});
              if (chiTietNhomDoiTuong) {
                refModalChiTietNhomDoiTuong.current?.open(chiTietNhomDoiTuong);
              }
            },
          };
        }}
        columns={(danhMucNhomDoiTuongColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableNhomDoiTuongDataType, item.title) : {}),
          };
        })} //định nghĩa cột của table
        dataSource={dataTableListNhomDoiTuong}
        title={renderHeaderTableNhomDoiTuong}
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong,
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
        }}
      />
      <ModalChiTietNhomDoiTuong ref={refModalChiTietNhomDoiTuong} />
    </div>
  );
};

export default memo(NhomDoiTuongContent);
