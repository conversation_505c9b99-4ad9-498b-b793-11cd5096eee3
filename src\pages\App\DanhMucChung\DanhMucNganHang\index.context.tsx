import {createContext, useContext} from "react";
import {DanhMucNganHangContextProps} from "./index.model";
export const DanhMucNganHangContext = createContext<DanhMucNganHangContextProps>({
  loading: false,
  tongSoDong: 0,

  filterParams: {},
  setFilterParams: () => {},
  danhSachNganHangPhanTrang: [],
  layDanhSachNganHangPhanTrang: () => {},
  onUpdateDanhMucNganHang: () => Promise.resolve(null),
  layChiTietNganHang: () => Promise.resolve(null),
  listDoiTac: [],
  getListDoiTac: () => Promise.resolve(),
});
export const useDanhMucNganHangContext = () => useContext(DanhMucNganHangContext);
