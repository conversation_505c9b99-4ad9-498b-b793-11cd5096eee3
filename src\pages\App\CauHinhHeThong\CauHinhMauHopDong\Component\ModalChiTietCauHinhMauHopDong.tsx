import React, {forwardRef, memo, useCallback, useContext, useEffect, useImperativeHandle, useMemo, useState, useRef} from "react";
import {Form, Modal, Row, Col, Flex, Input, Button} from "antd";
import {ArrowLeftOutlined, CheckOutlined, FolderOpenOutlined} from "@ant-design/icons";

import {FormInput, HeaderModal, Popcomfirm, ModalQuanLyFileCaNhan} from "@src/components";
import {IFormInput} from "@src/@types";
import {parseDateTime, formatDateTimeToNumber} from "@src/utils";

import CauHinhMauHopDongContext from "../index.context";
import {createModalDropdownOptions} from "../index.configs";
import {
  FormTaoMoiCauHinhMauHopDong, 
  MODAL_SETTINGS,
  IModalChiTietCauHinhMauHopDongRef,
  IModalChiTietCauHinhMauHopDongProps
} from "./index.configs";

/**
 * Modal này dùng để tạo mới và chỉnh sửa thông tin cấu hình mẫu hợp đồng
 */
const ModalChiTietCauHinhMauHopDongComponent = forwardRef<IModalChiTietCauHinhMauHopDongRef, IModalChiTietCauHinhMauHopDongProps>(
  ({onAfterSave}, ref) => {
    // ===== CONTEXT & FORM =====
    const {capNhatChiTietCauHinhMauHopDong, loading, listSanPham} = useContext(CauHinhMauHopDongContext);
    const [form] = Form.useForm();

    // ===== STATE MANAGEMENT =====
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [chiTietCauHinhMauHopDong, setChiTietCauHinhMauHopDong] = useState<any | null>(null);
    const [disableSubmit, setDisableSubmit] = useState<boolean>(true);
    const [previousDoiTac, setPreviousDoiTac] = useState<string | undefined>(undefined);
    const [previousNghiepVu, setPreviousNghiepVu] = useState<string | undefined>(undefined);
    const [selectedFileInfo, setSelectedFileInfo] = useState<{id: number; name: string} | null>(null);

    // ===== MODAL QUẢN LÝ FILE =====
    const modalQuanLyFileRef = useRef<any>(null);

    // ===== FORM VALUES WATCHING =====
    const formValues = Form.useWatch([], form);
    const selectedDoiTac = Form.useWatch('ma_doi_tac_ql', form);
    const selectedNghiepVu = Form.useWatch('nv', form);

    // ===== FORM CONFIGURATIONS =====
    const {ma_doi_tac_ql, nv, ma_sp, ten, ngay_ad, id_file, trang_thai} = FormTaoMoiCauHinhMauHopDong;

    // ===== DROPDOWN OPTIONS - USING CENTRALIZED FUNCTIONS =====
    const dropdownOptions = useMemo(() => 
      createModalDropdownOptions(listSanPham, selectedDoiTac, selectedNghiepVu),
      [listSanPham, selectedDoiTac, selectedNghiepVu]
    );

    // ===== IMPERATIVE HANDLE =====
    useImperativeHandle(ref, () => ({
      open: (data?: any) => {
        setChiTietCauHinhMauHopDong(data || null);
        setIsOpen(true);
      },
    }));

    // ===== EFFECTS =====
    // Reset fields khi đối tác thay đổi trong create mode (chỉ reset một lần)
    useEffect(() => {
      if (!chiTietCauHinhMauHopDong && isOpen) {
        // Reset nghiệp vụ và sản phẩm khi đối tác thay đổi
        if (selectedDoiTac !== previousDoiTac && previousDoiTac !== undefined) {
          form.setFieldValue('nv', undefined);
          form.setFieldValue('ma_sp', undefined);
        }
        
        // Reset sản phẩm khi nghiệp vụ thay đổi
        if (selectedNghiepVu !== previousNghiepVu && previousNghiepVu !== undefined) {
          form.setFieldValue('ma_sp', undefined);
        }
        
        setPreviousDoiTac(selectedDoiTac);
        setPreviousNghiepVu(selectedNghiepVu);
      }
    }, [selectedDoiTac, selectedNghiepVu, form, chiTietCauHinhMauHopDong, isOpen, previousDoiTac, previousNghiepVu]);

    // Khởi tạo form data khi modal mở
    useEffect(() => {
      if (chiTietCauHinhMauHopDong && isOpen) {
        // Chỉnh sửa - load data vào form
        let ngayAd = null;
        if (chiTietCauHinhMauHopDong.ngay_ad_date) {
          ngayAd = parseDateTime(chiTietCauHinhMauHopDong.ngay_ad_date);
        } else if (chiTietCauHinhMauHopDong.ngay_ad) {
          ngayAd = parseDateTime(chiTietCauHinhMauHopDong.ngay_ad);
        }
        
        const formData = {
          ...chiTietCauHinhMauHopDong,
          ngay_ad: ngayAd,
        };
        
        form.setFieldsValue(formData);
        
        // Set thông tin file nếu có
        if (chiTietCauHinhMauHopDong.id_file) {
          setSelectedFileInfo({
            id: Number(chiTietCauHinhMauHopDong.id_file),
            name: chiTietCauHinhMauHopDong.url_file ? 
              `File từ hệ thống (ID: ${chiTietCauHinhMauHopDong.id_file})` : 
              `File ID: ${chiTietCauHinhMauHopDong.id_file}`
          });
        }
        
      } else if (!chiTietCauHinhMauHopDong && isOpen) {
        // Tạo mới - set giá trị mặc định và reset previous values
        form.resetFields();
        form.setFieldsValue({
          trang_thai: "D",
        });
        setPreviousDoiTac(undefined);
        setPreviousNghiepVu(undefined);
        setSelectedFileInfo(null);
      }
    }, [chiTietCauHinhMauHopDong, isOpen, form]);

    // Validation real-time cho form
    useEffect(() => {
      if (!isOpen) return;

      form
        .validateFields({validateOnly: true})
        .then(() => {
          setDisableSubmit(false);
        })
        .catch(() => {
          setDisableSubmit(true);
        });
    }, [form, formValues, isOpen]);

    // ===== HANDLERS =====
    const closeModal = useCallback(() => {
      setIsOpen(false);
      setChiTietCauHinhMauHopDong(null);
      setSelectedFileInfo(null);
      form.resetFields();
    }, [form]);

    const onConfirm = async () => {
      try {
        const values = form.getFieldsValue();
        
        const btValue = chiTietCauHinhMauHopDong?.bt ? Number(chiTietCauHinhMauHopDong.bt) : undefined;
        const ngayAdValue = formatDateTimeToNumber(values.ngay_ad);
        
        const submitData = {
          ...values,
          ngay_ad: ngayAdValue,
          bt: btValue,
        };

        await capNhatChiTietCauHinhMauHopDong(submitData);
        closeModal();
        onAfterSave?.();
      } catch (error) {
        console.error("onConfirm error", error);
      }
    };

    /**
     * Xử lý mở modal chọn file
     */
    const handleChonFile = useCallback(() => {
      if (modalQuanLyFileRef.current?.open) {
        modalQuanLyFileRef.current.open();
      }
    }, []);

    /**
     * Xử lý khi chọn file từ modal
     */
    const handleChonFileSuccess = useCallback((filesSelected: any[]) => {
      console.log("handleChonFileSuccess",filesSelected)
      if (filesSelected && filesSelected.length > 0) {
        const selectedFile = filesSelected[0]; // Chỉ lấy file đầu tiên
        
        // Lấy id_file từ selectedFile - field 'id' trong File.GetFolder.IGetFolder
        const fileId = selectedFile.id;
        const fileName = selectedFile.ten_alias || selectedFile.ten || 'File không tên';
        
        if (fileId) {
          // Set giá trị id_file vào form
          form.setFieldValue('id_file', Number(fileId));
          
          // Lưu thông tin file để hiển thị
          setSelectedFileInfo({
            id: Number(fileId),
            name: fileName
          });
          
          // Đóng modal sau khi set thành công
          if (modalQuanLyFileRef.current?.close) {
            modalQuanLyFileRef.current.close();
          }
        } else {
          console.warn("Không tìm thấy id file trong selectedFile:", selectedFile);
        }
      }
    }, [form]);

    // ===== RENDER FUNCTIONS =====
    const renderFooter = () => {
      return (
        <div>
          <Button type="default" onClick={closeModal} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
            {MODAL_SETTINGS.buttons.cancel}
          </Button>
          <Popcomfirm
            title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
            onConfirm={onConfirm}
            okText="Lưu"
            description="Bạn có chắc muốn lưu thông tin?"
            disabled={disableSubmit}
          >
            <Button
              type="primary"
              loading={loading}
              disabled={disableSubmit}
              className="w-40"
              icon={<CheckOutlined />}
            >
              {chiTietCauHinhMauHopDong ? MODAL_SETTINGS.buttons.update : MODAL_SETTINGS.buttons.create}
            </Button>
          </Popcomfirm>
        </div>
      );
    };

    const renderFormColumn = (props: IFormInput, span: number = 12) => {
      return (
        <Col span={span}>
          <FormInput {...props} />
        </Col>
      );
    };

    /**
     * Render custom component cho chọn file
     */
    const renderCustomFileInput = (props: IFormInput, span: number = 12) => {
      // Lấy giá trị hiện tại của id_file từ form
      const currentFileId = Form.useWatch('id_file', form);
      
      // Tạo display text dựa trên thông tin file đã chọn
      const getDisplayText = () => {
        if (selectedFileInfo && currentFileId === selectedFileInfo.id) {
          return selectedFileInfo.name;
        }
        if (currentFileId) {
          return `File ID: ${currentFileId}`;
        }
        return '';
      };
      
      return (
        <Col span={span}>
          <Form.Item
            label={props.label}
            name={props.name}
            rules={props.rules}
            required={props.rules?.some(rule => (rule as any)?.required)}
          >
            <Input.Group compact>
              <Input
                style={{ width: 'calc(100% - 40px)' }}
                placeholder={getDisplayText() || props.placeholder}
                value={getDisplayText()}
                readOnly
              />
              <Button
                type="primary"
                icon={<FolderOpenOutlined />}
                style={{ width: '40px' }}
                onClick={handleChonFile}
                title="Chọn file từ hệ thống"
              />
            </Input.Group>
          </Form.Item>
        </Col>
      );
    };

    const renderForm = () => (
      <Form form={form} layout="vertical">
        <Row gutter={16}>
          {renderFormColumn(ten,12)}
          {renderFormColumn({...ma_doi_tac_ql, options: dropdownOptions.doiTac},12)}
        </Row>
        
        <Row gutter={16}>
          {renderFormColumn({...nv, options: dropdownOptions.nghiepVu},12)}
          {renderFormColumn({...ma_sp, options: dropdownOptions.sanPham},12)}  
        </Row>
        
        <Row gutter={16}>
          {renderCustomFileInput(id_file, 12)}
          {renderFormColumn(ngay_ad,6)}
          {renderFormColumn({...trang_thai, options: dropdownOptions.trangThai},6)}
        </Row>
      </Form>
    );

    // ===== MAIN RENDER =====
    return (
      <Flex vertical gap="middle" align="flex-start">
        <Modal
          title={<HeaderModal 
            title={chiTietCauHinhMauHopDong ? `${chiTietCauHinhMauHopDong.ten}` : MODAL_SETTINGS.titles.create} 
            trang_thai_ten={chiTietCauHinhMauHopDong?.trang_thai_ten} 
            trang_thai={chiTietCauHinhMauHopDong?.trang_thai} 
          />}
          maskClosable={false}
          open={isOpen}
          onOk={() => closeModal()}
          onCancel={() => closeModal()}
          width={MODAL_SETTINGS.width}
          styles={{
            body: {
              paddingTop: "8px",
              paddingBottom: "16px",
            },
          }}
          footer={renderFooter()}
          destroyOnClose={MODAL_SETTINGS.destroyOnClose}
        >
           {/* Modal quản lý file cá nhân */}
        <ModalQuanLyFileCaNhan 
          ref={modalQuanLyFileRef} 
          onClickChonFile={handleChonFileSuccess}
        />
          {renderForm()}
        </Modal>
        
       
      </Flex>
    );
  },
);

ModalChiTietCauHinhMauHopDongComponent.displayName = "ModalChiTietCauHinhMauHopDong";
export default memo(ModalChiTietCauHinhMauHopDongComponent);
