/**
 * T<PERSON><PERSON> dụng: Tạo React Context để chia sẻ state và methods giữa các component
 */
import {createContext} from "react";

import {IDanhMucQuanHuyenProvider} from "./index.model";

//Tạo React Context để chia sẻ state và methods giữa các component
const DanhMucQuanHuyenContext = createContext<IDanhMucQuanHuyenProvider>({
  listQuanHuyen: [],
  listTinhThanh: [],
  loading: false,
  tongSoDong: 0,
  filterParams: {
    ma: "",
    ten: "",
    ma_tinh: "",
    ngay_ad: "",
    trang_thai: "",
    trang: 1,
    so_dong: 13,
  },
  searchQuanHuyen: async () => {},
  getChiTietQuanHuyen: async () => ({} as CommonExecute.Execute.IDanhMucQuanHuyen),
  capNhatChiTietQuanHuyen: async () => {},
  setFilterParams: () => {},
});

export default DanhMucQuanHuyenContext;
