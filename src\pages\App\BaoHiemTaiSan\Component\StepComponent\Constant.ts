import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {formatCurrencyUS, parseDateTime} from "@src/utils";
import {TableProps, Tag} from "antd";
import React from "react";

export const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};
//Hàm ép kiểu dùng dung
export const parseNumber = (value: any): number => {
  // Xử lý các trường hợp null, undefined, empty string
  if (value == null || value === "") return 0;

  // Nếu đã là number, kiểm tra NaN và trả về
  if (typeof value === "number") {
    return Number.isNaN(value) ? 0 : value;
  }

  // Nếu là string, làm sạch và parse
  if (typeof value === "string") {
    // Loại bỏ khoảng trắng đầu cuối
    const trimmed = value.trim();
    if (trimmed === "") return 0;

    // Loại bỏ dấu phẩy phân cách hàng nghìn và ký tự không phải số, dấu chấm, dấu trừ
    const cleaned = trimmed.replace(/[,]/g, "").replace(/[^0-9.-]/g, "");

    // Kiểm tra format hợp lệ (chỉ có 1 dấu chấm và 1 dấu trừ ở đầu)
    if (!/^-?\d*\.?\d*$/.test(cleaned)) return 0;

    const parsed = Number(cleaned);
    return Number.isNaN(parsed) ? 0 : parsed;
  }

  // Các trường hợp khác (boolean, object, array, etc.)
  return 0;
};

export const defaultFormValueTimKiemDoiTuongBaoHiemTaiSan: ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams = {
  so_id: 0,
  // gcn: "",
  // ten: "",
  nd_tim: "",
  trang: 1,
  so_dong: 14, // set khác default cho vừa màn hình
  ma_dvi_dong_tai: "",
  dong_tai: "",
};

//INTERFACE ĐỐI TƯỢNG
export interface TableDoiTuongColumnDataType {
  key: string;
  sott?: number; //CỘT 1
  ten?: string; //CỘT 2
  so_id?: number | string;
  so_id_dt?: number | string;
  tong_phi?: number | string;
  sdbs?: string; // Trạng thái đối tượng: N-Mới, S-Sửa đổi, K-Không sửa đổi, H-Kết thúc hiệu lực
  ap_dung: string;
  tl_dong?: string | number;
  tl_tai?: string | number;
  ma_dvi_dong_tai: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableDoiTuongColumn: TableProps<TableDoiTuongColumnDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "Tên đối tượng",
    dataIndex: "ten",
    key: "ten",
    width: "50%",
    align: "left",
    ellipsis: true,
    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px]"}, "");
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Phí BH",
    dataIndex: "tong_phi",
    key: "tong_phi",
    width: "25%",
    align: "right",
    ellipsis: true,
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
    },
  },
];
//option select đối tượng áp dụng
export const listDoiTuongApDungDongBH = [
  {ten: "Tất cả", ma: ""},
  {ten: "Đối tượng áp dụng", ma: "DONG_BH"},
];
//option select kiểu đồng
export const listKieuTai = [
  {ten: "Tái cố định", ma: "C"},
  {ten: "Tái tạm thời", ma: "T"},
];
//FORM TÌM KIẾM ĐỐI TƯỢNG
//INTERFACE FORM TÌM KIẾM ĐỐI TƯỢNG
export interface IFormTimKiemPhanTrangDoiTuongBaoHiemTaiSanFieldsConfig {
  so_id?: IFormInput;
  gcn?: IFormInput;
  ten?: IFormInput;
  nd_tim?: IFormInput;
  dong_tai?: IFormInput;
}
// KHƠI TẠO FORM TÌM KIẾM ĐỐI TƯỢNG
export const FormTimKiemDoiTuongBaoHiemTaiSan: IFormTimKiemPhanTrangDoiTuongBaoHiemTaiSanFieldsConfig = {
  nd_tim: {
    component: "input",
    // label: "Số GCN/BSX/số khung/số máy",
    name: "ten",
    placeholder: "Tên đối tượng",
  },
  dong_tai: {
    component: "select",
    // label: "Đối tượng áp dụng",
    name: "dong_tai",
    placeholder: "Chọn đối tượng áp dụng",
    options: listDoiTuongApDungDongBH,
  },
};
//FORM THÊM /SỬA ĐỐI TƯỢNG
//INTERFACE FORM THÊM /SỬA ĐỐI TƯỢNG
export interface IFormDoiTuongBaoHiemTaiSan {
  so_id: IFormInput;
  so_id_dt: IFormInput;
  ten: IFormInput;
  gcn: IFormInput;
  nhom_dt: IFormInput;
  latitude: IFormInput;
  longitude: IFormInput;
  gia_tri: IFormInput;
  gio_hl: IFormInput;
  ngay_hl: IFormInput;
  gio_kt: IFormInput;
  ngay_kt: IFormInput;
  ngay_cap: IFormInput;
  vip: IFormInput;
  dk: IFormInput;
  dkbs: IFormInput;
  vi_tri: IFormInput;
  tinh_thanh: IFormInput;
  phuong_xa: IFormInput;
  dchi: IFormInput;
  ttinh: IFormInput;
  dt_ttrr: IFormInput;
}

//KHỞI TẠO FORM THÊM /SỬA ĐỐI TƯỢNG
export const FormTaoMoiDoiTuongBaoHiemTaiSan: IFormDoiTuongBaoHiemTaiSan = {
  so_id: {
    // component: "input",
    // label: "Số ID",
    name: "so_id",
    // placeholder: "Nhập số ID",
    // rules: [ruleRequired],
    className: "hidden",
  },
  so_id_dt: {
    // component: "input",
    // label: "Số ID đối tác",
    name: "so_id_dt",
    // placeholder: "Nhập số ID đối tác",
    // rules: [ruleRequired],
    className: "hidden",
  },
  ten: {
    // component: "select",
    label: "Tên đối tượng",
    name: "ten",
    placeholder: "Tên đối tượng",
    rules: [ruleRequired],
  },
  gcn: {
    component: "input",
    label: "Giấy chứng nhận",
    name: "gcn",
    placeholder: "Nhập giấy chứng nhận",
    rules: [ruleRequired],
    normalize: (value: string) => value?.toUpperCase(),
  },
  gia_tri: {
    component: "input-price",
    label: "Giá trị tài sản",
    name: "gia_tri",
    placeholder: "0",
    rules: [ruleRequired],
  },
  nhom_dt: {
    component: "select",
    label: "Nhóm đối tượng",
    name: "nhom_dt",
    placeholder: "Chọn nhóm đối tượng",
    rules: [ruleRequired],
  },
  latitude: {
    component: "input",
    label: "Kinh độ",
    name: "latitude",
    placeholder: "Nhập kinh độ",
    rules: [ruleRequired],
  },
  longitude: {
    component: "input",
    label: "Vĩ độ",
    name: "longitude",
    placeholder: "Nhập vĩ độ",
    rules: [ruleRequired],
  },
  gio_hl: {
    component: "time-picker",
    label: "Giờ hiệu lực",
    name: "gio_hl",
    placeholder: "Chọn giờ",
    className: "w-full",
    rules: [ruleRequired],
  },
  ngay_hl: {
    component: "date-picker",
    label: "Ngày hiệu lực",
    name: "ngay_hl",
    placeholder: "Chọn ngày",
    className: "w-full",
    rules: [ruleRequired],
  },
  gio_kt: {
    component: "time-picker",
    label: "Giờ kết thúc",
    name: "gio_kt",
    className: "w-full",
    placeholder: "Chọn giờ",
    rules: [ruleRequired],
  },
  ngay_kt: {
    component: "date-picker",
    label: "Ngày kết thúc",
    name: "ngay_kt",
    className: "w-full",
    placeholder: "Chọn ngày",
    rules: [ruleRequired],
  },
  ngay_cap: {
    component: "date-picker",
    label: "Ngày cấp",
    name: "ngay_cap",
    placeholder: "Chọn ngày",
    className: "w-full",
    rules: [ruleRequired],
  },
  vi_tri: {
    component: "select",
    label: "Vị trí",
    name: "vi_tri",
    placeholder: "Nhập vị trí",
    rules: [ruleRequired],
  },
  tinh_thanh: {
    component: "select",
    label: "Tỉnh thành",
    name: "tinh_thanh",
    placeholder: "Chọn tỉnh thành",
    // rules: [ruleRequired],
  },
  phuong_xa: {
    component: "select",
    label: "Phường xã",
    name: "phuong_xa",
    placeholder: "Chọn phường xã",

    // rules: [ruleRequired],
  },
  dchi: {
    component: "input",
    label: "Địa chỉ",
    name: "dchi",
    placeholder: "Nhập địa chỉ",
    // rules: [ruleRequired],
  },
  vip: {
    component: "select",
    label: "Hợp đồng VIP",
    name: "vip",
    placeholder: "Chọn hợp đồng VIP",
  },
  dt_ttrr: {
    component: "select",
    label: "Đối tượng tích tụ rủi ro",
    name: "dt_ttrr",
    placeholder: "Nhập đối tượng tích tụ rủi ro",
  },
  dk: {
    name: "dk",
    className: "hidden",
  },
  dkbs: {
    name: "dkbs",
    className: "hidden",
  },
  ttinh: {
    name: "ttinh",
    className: "hidden",
  },
};
//option select hợp đồng VIP
export const listHDVipSelect = [
  {ma: "VIP", ten: "VIP"},
  {ma: "", ten: "Không"},
];
//khởi tạo form chi tiết đối tượng
export const initFormFieldsDoiTuong = (form: any, chiTietDoiTuongBaoHiemTaiSan: any) => {
  if (!chiTietDoiTuongBaoHiemTaiSan) return;
  //chi chuyển sang đối tượng khác thì reset form
  form.resetFields();
  const {gcn, gcn_dk, gcn_dkbs, gcn_ct} = chiTietDoiTuongBaoHiemTaiSan;

  const DATE_TIME_FIELDS = ["ngay_cap", "ngay_hl", "ngay_kt", "gio_hl", "gio_kt"];

  const fields = Object.entries(gcn || {}).map(([name, value]) => {
    if (!value) return {name: "", value: ""};
    //formart ngày
    if (DATE_TIME_FIELDS.includes(name)) {
      return {
        name,
        value: parseDateTime(value),
        errors: [],
      };
    }

    return {name, value, errors: []};
  });

  // Set fields from gcn
  form.setFields(fields);

  // Set fields from gcn_dk
  if (gcn_dk) {
    form.setFieldsValue({dk: gcn_dk});
    form.setFields([{name: "dk", touched: true}]);
  }
  if (gcn_ct && Array.isArray(gcn_ct)) {
    //nếu giá trị thuộc tính kiểu date thì đổi thành dạng hiển thị được trên input
    const newData = gcn_ct.map(item => {
      if (item.kieu_dl === "DATE") {
        return {...item, gia_tri: parseDateTime(item.gia_tri)};
      }
      return item;
    });
    const ts_ct_data = newData.map(item => ({
      ma_thuoc_tinh: item.ma_thuoc_tinh,
      ten_thuoc_tinh: item.ten_thuoc_tinh,
      gia_tri: item.gia_tri,
      kieu_dl: item.kieu_dl,
    }));
    form.setFieldValue("gcn_ct", ts_ct_data);
  }
  // Set fields from gcn_dkbs
  if (gcn_dkbs) {
    form.setFieldsValue({dkbs: gcn_dkbs});
    form.setFields([{name: "dkbs", touched: true}]);
  }
};
export const VI_TRI = [
  {ma: "C", ten: "Cố định"},
  {ma: "K", ten: "Không cố định"},
];
export interface TableDoiTuongCTDataType {
  key: string;
  stt: number;
  gia_tri: string;
  ma_thuoc_tinh: string;
  ten_thuoc_tinh: string;
  kieu_dl: string;
  // hanh_dong?: () => JSX.Element | null; // Định nghĩa hanh_dong là hàm trả về JSX.Element;
}
export const doiTuongCTColumns: TableProps<TableDoiTuongCTDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã thuộc tính", dataIndex: "ma_thuoc_tinh", key: "ma_thuoc_tinh", width: 150, className: "col-ma-thuoc-tinh", align: "center", ...defaultTableColumnsProps},
  {title: "Tên thuộc tính", dataIndex: "ten_thuoc_tinh", key: "ten_thuoc_tinh", width: 230, align: "left", ...defaultTableColumnsProps},
  {title: "Kiểu dữ liệu", dataIndex: "kieu_dl", key: "kieu_dl", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Giá trị thuộc tính", dataIndex: "gia_tri", key: "gia_tri", align: "center", ...defaultTableColumnsProps},
  // {
  //   title: "Xóa",
  //   dataIndex: "hanh_dong",
  //   // key: "hanh_dong",
  //   width: 100,
  //   render: (_, record) => record.hanh_dong?.(),
  //   ...defaultTableColumnsProps,
  // },
];
export type DataIndexDoiTuongCT = keyof TableDoiTuongCTDataType;
//KIEU_DU_LIEU DATE, NUMBER, TEXT
export const KIEU_DU_LIEU = [
  {ten: "DATE", ma: "DATE"},
  {ten: "NUMBER", ma: "NUMBER"},
  {ten: "TEXT", ma: "TEXT"},
];
//THÔNG TIN ĐỒNG BẢO HIỂM TABLE

//INTERFACE TT IN ĐỒNG BẢO HIỂM TABLE
export interface TableCauHinhDongBaoHiemDataType {
  kieu_dong?: string;
  loai_dong?: string;
  loai_dong_ten?: string;
  ma_doi_tac?: string;
  ma_doi_tac_ql?: string;
  ma_dvi_dong?: string;
  ten_dvi_dong?: string;
  nv?: string;
  sl_dt_bh?: number;
  sl_goi_bh?: number;
  sl_qloi_bh?: number;
  tl_dong?: number;
  sott?: number;
  key: string;
  so_tham_chieu?: string;
  tien_nhuong_dong?: number;
  tien_con?: number;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableCauHinhDongBaoHiemColumn: TableProps<TableCauHinhDongBaoHiemDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    width: colWidthByKey.sott,
  },
  {
    ...defaultTableColumnsProps,
    title: "Loại đồng",
    dataIndex: "loai_dong_ten",
    key: "loai_dong_ten",
    width: 90,

    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px]"}, "");
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Kiểu đồng",
    dataIndex: "kieu_dong",
    key: "kieu_dong",
    width: 90,
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên đơn vị đồng",
    dataIndex: "ten_dvi_dong",
    key: "ten_dvi_dong",
  },
  {
    ...defaultTableColumnsProps,
    title: "TL đồng (%)",
    dataIndex: "tl_dong",
    key: "tl_dong",
    width: 100,
  },
  {
    ...defaultTableColumnsProps,
    title: "Số tham chiếu/Hợp đồng/SĐBS",
    dataIndex: "so_tham_chieu",
    key: "so_tham_chieu",
    width: 200,
  },
  {
    ...defaultTableColumnsProps,
    title: "Tiền nhượng đồng",
    dataIndex: "tien_nhuong_dong",
    key: "tien_nhuong_dong",
    width: 150,
    align: "right",
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
      return text;
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Tiền giữ lại",
    dataIndex: "tien_con",
    key: "tien_con",
    width: 150,
    align: "right",
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
      return text;
    },
  },
  {...defaultTableColumnsProps, title: "Đối tượng áp dụng", dataIndex: "sl_dt_bh", key: "sl_dt_bh", align: "center", width: 200},
  {
    ...defaultTableColumnsProps,
    // title: "Hành động",
    dataIndex: "action",
    key: "action",
    width: 60,
  },
];

export interface IFormNhapCauHinhDong {
  ma_dvi_dong: IFormInput;
  loai_dong: IFormInput;
  kieu_dong: IFormInput;
  so_id: IFormInput;
  tl_dong: IFormInput;
  so_tham_chieu: IFormInput;
}
export const formCauHinhDongInputConfigs: IFormNhapCauHinhDong = {
  so_id: {
    name: "so_id",
    className: "hidden",
  },
  ma_dvi_dong: {
    name: "ma_dvi_dong",
    component: "select",
    label: "Mã đơn vị đồng",
    placeholder: "Mã đơn vị",
    rules: [ruleRequired],
  },
  loai_dong: {
    name: "loai_dong",
    component: "select",
    label: "Loại đồng",
    placeholder: "Chọn loại đồng",
    rules: [ruleRequired],
  },
  kieu_dong: {
    name: "kieu_dong",
    component: "select",
    label: "Kiểu đồng NBH",
    placeholder: "Nhập số HĐ đầu",
    rules: [ruleRequired],
  },
  tl_dong: {
    name: "tl_dong",
    component: "input",
    label: "Tỷ lệ đồng",
    placeholder: "0",
    className: "!text-right",
    rules: [ruleRequired],
  },
  so_tham_chieu: {
    name: "so_tham_chieu",
    component: "input",
    label: "Số tham chiếu/Hợp đồng/SĐBS",
    placeholder: "Số tham chiếu/Hợp đồng/SĐBS",
    rules: [ruleRequired],
  },
};
//option select loại đồng
export const listLoaiDong = [
  {ten: "Đồng trong", ma: "T"},
  {ten: "Đồng ngoài", ma: "N"},
];

//option select kiểu đồng
export const listKieuDong = [
  {ten: "LEADER", ma: "LEADER"},
  {ten: "FOLLOW", ma: "FOLLOW"},
];

//THÔNG TIN TÁI BẢO HIỂM TABLE

//INTERFACE TT IN ĐỒNG BẢO HIỂM TABLE
export interface TableCauHinhTaiBHDataType {
  key?: string | number;
  kieu_tai?: string;
  kieu_tai_ten?: string;
  ma_doi_tac?: string;
  ma_doi_tac_ql?: string;
  ma_dvi_tai?: string;
  ten_dvi_tai?: string;
  nv?: string;
  sl_dt_bh?: number;
  sl_goi_bh?: number;
  sl_qloi_bh?: number;
  sott?: number;
  tl_tai?: number;
  so_tham_chieu?: string;
  tien_nhuong_tai?: number;
  tien_con?: number;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableCauHinhTaiBHColumn: TableProps<TableCauHinhTaiBHDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    width: colWidthByKey.sott,
  },

  {
    ...defaultTableColumnsProps,
    title: "Kiểu tái",
    dataIndex: "kieu_tai_ten",
    key: "kieu_tai_ten",
    width: 100,
    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px]"}, "");
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Tên đơn vị tái",
    dataIndex: "ten_dvi_tai",
    key: "ten_dvi_tai",
  },
  {
    ...defaultTableColumnsProps,
    title: "TL tái (%)",
    dataIndex: "tl_tai",
    key: "tl_tai",
    width: 100,
  },
  {
    ...defaultTableColumnsProps,
    title: "Số tham chiếu/Hợp đồng/SĐBS",
    dataIndex: "so_tham_chieu",
    key: "so_tham_chieu",
    width: 250,
  },
  {
    ...defaultTableColumnsProps,
    title: "Tiền nhượng tái",
    dataIndex: "tien_nhuong_tai",
    key: "tien_nhuong_tai",
    width: 150,
    align: "right",
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
      return text;
    },
  },
  // {
  //   ...defaultTableColumnsProps,
  //   title: "Tiền giữ lại",
  //   dataIndex: "tien_con",
  //   key: "tien_con",
  //   width: 150,
  //   align: "right",
  //   render: (text: any) => {
  //     if (typeof text === "number") {
  //       return formatCurrencyUS(text);
  //     }
  //     return text;
  //   },
  // },
  {...defaultTableColumnsProps, title: "Đối tượng áp dụng", dataIndex: "sl_dt_bh", key: "sl_dt_bh", align: "center", width: 200},
  {
    ...defaultTableColumnsProps,
    // title: "Hành động",
    dataIndex: "action",
    key: "action",
    width: colWidthByKey.sott,
  },
];

export interface IFormNhapCauHinhTaiBH {
  ma_dvi_tai: IFormInput;
  kieu_tai: IFormInput;
  so_id: IFormInput;
  tl_tai: IFormInput;
  so_tham_chieu: IFormInput;
}
export const formCauHinhTaiBHInputConfigs: IFormNhapCauHinhTaiBH = {
  so_id: {
    name: "so_id",
    className: "hidden",
  },
  ma_dvi_tai: {
    name: "ma_dvi_tai",
    component: "select",
    label: "Mã đơn vị tái",
    placeholder: "Mã đơn vị",
    rules: [ruleRequired],
  },
  kieu_tai: {
    name: "kieu_tai",
    component: "select",
    label: "Kiểu tái NBH",
    placeholder: "Nhập số HĐ đầu",
    rules: [ruleRequired],
  },
  tl_tai: {
    name: "tl_tai",
    component: "input",
    label: "Tỷ lệ tái",
    placeholder: "0",
    className: "!text-right",
    rules: [ruleRequired],
  },
  so_tham_chieu: {
    name: "so_tham_chieu",
    component: "input",
    label: "Số tham chiếu/Hợp đồng/SĐBS",
    placeholder: "Số tham chiếu/Hợp đồng/SĐBS",
    rules: [ruleRequired],
  },
};
export const tableDoiTuongApDungDongBHColumn: TableProps<TableDoiTuongColumnDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    width: "10%",
  },
  {
    ...defaultTableColumnsProps,
    title: "Áp dụng",
    dataIndex: "ap_dung",
    key: "ap_dung",
    width: "15%",
  },
  {
    ...defaultTableColumnsProps,
    title: "Biển xe / Số khung / Số máy",
    dataIndex: "bien_xe",
    key: "bien_xe",
    width: "50%",
    align: "left",
    ellipsis: true,
    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px]"}, "");
    },
  },

  {
    ...defaultTableColumnsProps,
    title: "Tỷ lệ",
    dataIndex: "tl_dong",
    key: "tl_dong",
    width: "20%",
  },
];
//FORM TÌM KIẾM ĐỐI TƯỢNG
//INTERFACE FORM TÌM KIẾM ĐỐI TƯỢNG TÁI BẢO HIỂM
export const listDoiTuongApDungTaiBH = [
  {ten: "Tất cả", ma: ""},
  {ten: "Đối tượng áp dụng", ma: "TAI_BH"},
];
export interface IFormTimKiemPhanTrangDoiTuongBaoHiemTaiSanFieldsConfig {
  so_id?: IFormInput;
  gcn?: IFormInput;
  ten?: IFormInput;
  nd_tim?: IFormInput;
  dong_tai?: IFormInput;
}

export const FormTimKiemDoiTuongTaiBaoHiem: IFormTimKiemPhanTrangDoiTuongBaoHiemTaiSanFieldsConfig = {
  so_id: {
    name: "so_id",
  },
  gcn: {
    component: "input",
    label: "Số GCN",
    name: "gcn",
    open: false,
    placeholder: "Nhập số GCN",
  },
  ten: {
    component: "input",
    label: "Tên đối tượng",
    name: "ten",
    placeholder: "Tên đối tượng",
  },
  nd_tim: {
    component: "input",
    // label: "Số GCN/BSX/số khung/số máy",
    name: "nd_tim",
    placeholder: "Số GCN/BSX/số khung/số máy",
  },
  dong_tai: {
    component: "select",
    // label: "Đối tượng áp dụng",
    name: "dong_tai",
    placeholder: "Chọn đối tượng áp dụng",
    options: listDoiTuongApDungTaiBH,
  },
};
export const tableDoiTuongApDungTaiBHColumn: TableProps<TableDoiTuongColumnDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    width: "10%",
  },
  {
    ...defaultTableColumnsProps,
    title: "Áp dụng",
    dataIndex: "ap_dung",
    key: "ap_dung",
    width: "15%",
  },
  {
    ...defaultTableColumnsProps,
    title: "Biển xe / Số khung / Số máy",
    dataIndex: "bien_xe",
    key: "bien_xe",
    width: "50%",
    align: "left",
    ellipsis: true,
    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px]"}, "");
    },
  },

  {
    ...defaultTableColumnsProps,
    title: "Tỷ lệ",
    dataIndex: "tl_tai",
    key: "tl_tai",
    width: "20%",
  },
];
//THÔNG TIN THANH TOÁN
//INTERFACE TT THANH TOÁN
export interface TableThongTinKyThanhToanDataType {
  bt: number;
  ky_tt: number;
  ky_tt_date: string;
  loai: string;
  loai_ten: string | null;
  ma_doi_tac: string;
  ma_doi_tac_ql: string;
  ngay_cap_nhat: string | null;
  ngay_tao: string | null;
  ngay_tt: string | null;
  ngay_tt_date: string;
  nguoi_cap_nhat: string | null;
  nguoi_tao: string | null;
  so_hd: string | null;
  so_id: number | null;
  so_id_d: number;
  so_id_g: number | null;
  so_id_ky_ttoan: number;
  so_tien: number;
  so_tien_da_tt: number | null;
  trang_thai: string | null;
  key: string;
  sott: number;
  rowSpanKyTT?: number;
  rowSpanSoTien?: number;
  sttKyTT?: number;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableThongTinThanhToanColumn: TableProps<TableThongTinKyThanhToanDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "sttKyTT",
    key: "sttKyTT",
    width: colWidthByKey.sott,
    align: "center",
    onCell: (record: any) => ({rowSpan: record.rowSpanKyTT ?? 1, className: "!py-1 text-[11px]"}),
    render: (text: any, record: any) => (record.sttKyTT ? record.sttKyTT : null),
  },
  {
    ...defaultTableColumnsProps,
    title: "Kỳ t/toán",
    dataIndex: "ky_tt_date",
    key: "ky_tt_date",
    width: 90,
    align: "center",
    onCell: (record: any) => ({rowSpan: record.rowSpanKyTT ?? 1, className: "!py-1 text-[11px] font-semibold"}),
    render: (text: any, record: any, index: number) => {
      return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px] "}, "");
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Số tiền",
    dataIndex: "so_tien",
    key: "so_tien",
    width: 110,
    align: "right",
    ellipsis: {showTitle: false},
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
      return text;
    },
    onCell: (record: any) => ({rowSpan: record.rowSpanSoTien ?? 1, className: "!py-1 text-[11px] !font-semibold"}),
  },
  {
    ...defaultTableColumnsProps,
    title: "Loại",
    dataIndex: "loai_ten",
    key: "loai_ten",
    width: 80,
    align: "center",
    render: (text: any, record: any) => {
      if (text === "Tăng phí") {
        return React.createElement("span", {style: {color: "#52c41a"}}, text);
      }
      if (text === "Hoàn phí") {
        return React.createElement("span", {style: {color: "#ff4d4f"}}, text);
      }
      return record.loai_ten;
    },
  },
  {...defaultTableColumnsProps, title: "Ngày t/toán", dataIndex: "ngay_tt_date", key: "ngay_tt_date", width: 100, align: "center"},
  {
    ...defaultTableColumnsProps,
    title: "Tiền đã t/toán",
    dataIndex: "so_tien_da_tt",
    key: "so_tien_da_tt",
    width: 120,
    align: "right",
    ellipsis: {showTitle: false},
    render: (text: any) => {
      if (typeof text === "number") {
        if (text < 0) {
          return React.createElement("span", {style: {color: "#ff4d4f"}}, formatCurrencyUS(text));
        }
        return formatCurrencyUS(text);
      }
      return text;
    },
  },
  {...defaultTableColumnsProps, title: "Số chứng từ", dataIndex: "so_ct", key: "so_ct", width: 120, align: "center"},
  {...defaultTableColumnsProps, title: "Số HĐ/SĐBS", dataIndex: "so_hd", key: "so_hd", width: 120, align: "center"},
  {...defaultTableColumnsProps, title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, align: "center"},
  {...defaultTableColumnsProps, title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, align: "center"},
  {...defaultTableColumnsProps, title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center"},
  {...defaultTableColumnsProps, title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center"},
];

export interface IFormNhapKyThanhToan {
  ky_tt: IFormInput;
  so_tien: IFormInput;
  so_hd_d: IFormInput;
}
export const formNhapKyThanhToanInputConfigs: IFormNhapKyThanhToan = {
  ky_tt: {
    name: "ky_tt",
    component: "date-picker",
    label: "Kỳ thanh toán",
    placeholder: "Chọn kỳ thanh toán",
    rules: [ruleRequired],
  },
  so_tien: {
    name: "so_tien",
    component: "input-price",
    label: "Số tiền",
    placeholder: "0",
    rules: [ruleRequired],
  },
  so_hd_d: {
    name: "so_hd_d",
    component: "input",
    label: "Số HĐ đầu",
    placeholder: "Nhập số HĐ đầu",
    rules: [ruleRequired],
    disabled: true,
  },
};
//TAB NGÀY THANH TOÁN
export interface TableNgayThanhToanDataType {
  key: string;
  stt: number;
  ngay_tt: string;
  so_tien_da_tt: number;
  bt: number;
  ten_loai: string;
  so_ct: string;
}

export const tableNgayThanhToanColumns: TableProps<TableNgayThanhToanDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, className: "no-hover-table"},
  {...defaultTableColumnsProps, title: "Ngày thanh toán", dataIndex: "ngay_tt", key: "ngay_tt", className: "no-hover-table"},
  {...defaultTableColumnsProps, title: "Số chứng từ", dataIndex: "so_ct", key: "so_ct", width: 150, className: "no-hover-table"},
  {
    ...defaultTableColumnsProps,
    title: "Số tiền đã thanh toán",
    dataIndex: "so_tien_da_tt",
    key: "so_tien_da_tt",
    width: 150,
    align: "right",
    ellipsis: true,
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
      return text;
    },
  },

  {...defaultTableColumnsProps, title: "Tên loại", dataIndex: "ten_loai", key: "ten_loai", width: 80},
  {
    ...defaultTableColumnsProps,
    // title: "Hành động",
    dataIndex: "action",
    key: "action",
    width: 80,
  },
];
//TAB DANH SÁCH TÀI SẢN

export interface IFormTimKiemPhanTrangDanhSachTaiSanFieldsConfig {
  nd_tim?: IFormInput;
}

export const FormTimKiemDanhSachTaiSan: IFormTimKiemPhanTrangDanhSachTaiSanFieldsConfig = {
  nd_tim: {
    component: "input",
    // label: "Số GCN/BSX/số khung/số máy",
    name: "nd_tim",
    placeholder: "Tên tài sản",
  },
};
export interface TableTaiSanColumnDataType {
  key: string;
  sott?: number; //CỘT 1
  ten?: string; //CỘT 2
  so_id?: number | string;
  so_id_dt?: number | string;
  so_id_ts?: number;
  so_luong?: number;
  don_gia?: number;
  tong_tien?: number;
  nam_sx?: number;
  han_sd?: number;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai?: string;
  trang_thai_ten?: string;
}
export const tableTaiSanColumn: TableProps<TableTaiSanColumnDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "sott", key: "sott", width: colWidthByKey.sott},
  {...defaultTableColumnsProps, title: "Tên tài sản", dataIndex: "ten", key: "ten", width: 200, align: "left"},
  {...defaultTableColumnsProps, title: "SL", dataIndex: "so_luong", key: "so_luong", width: 50},
  {
    ...defaultTableColumnsProps,
    title: "Đơn giá",
    dataIndex: "don_gia",
    key: "don_gia",
    width: 100,
    align: "right",
    ellipsis: {showTitle: false},
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
      return text;
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Tổng tiền",
    dataIndex: "tong_tien",
    key: "tong_tien",
    width: 100,
    align: "right",
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
      return text;
    },
  },
  {...defaultTableColumnsProps, title: "Năm SX", dataIndex: "nam_sx", key: "nam_sx", width: 100},
  {...defaultTableColumnsProps, title: "HSD", dataIndex: "han_sd", key: "han_sd", width: 100},
  {...defaultTableColumnsProps, title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao},
  {...defaultTableColumnsProps, title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao},
  {...defaultTableColumnsProps, title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat},
  {...defaultTableColumnsProps, title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat},
  {...defaultTableColumnsProps, title: "Trạng thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 120},
  // {
  //   ...defaultTableColumnsProps,
  //   title: "Phí BH",
  //   dataIndex: "tong_phi",
  //   key: "tong_phi",
  //   width: "25%",
  //   align: "right",
  //   ellipsis: true,
  //   // render: (text: any) => <NumericFormat value={text} displayType="text" thousandSeparator="," decimalSeparator="." decimalScale={2} style={{textAlign: "right"}} />,
  // },
];
export type DataIndexTaiSan = keyof TableTaiSanColumnDataType;
export const radioItemTrangThaiTaiSanTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];
//FormchiTietDanhSachTaiSan
export interface IFormchiTietDanhSachTaiSan {
  so_id?: IFormInput;
  so_id_dt?: IFormInput;
  so_id_ts?: IFormInput;
  ten?: IFormInput;
  so_luong?: IFormInput;
  don_gia?: IFormInput;
  tong_tien?: IFormInput;
  nam_sx?: IFormInput;
  han_sd?: IFormInput;
  trang_thai?: IFormInput;
}
export const FormchiTietDanhSachTaiSan: IFormchiTietDanhSachTaiSan = {
  so_id: {
    name: "so_id",
    className: "hidden",
  },
  so_id_dt: {
    name: "so_id_dt",
    className: "hidden",
  },
  so_id_ts: {
    name: "so_id_ts",
    className: "hidden",
  },
  ten: {
    component: "input",
    label: "Tên tài sản",
    name: "ten",
    placeholder: "Tên tài sản",
    rules: [ruleRequired],
  },
  so_luong: {
    component: "input-price",
    label: "Số lượng",
    name: "so_luong",
    placeholder: "Số lượng",
    rules: [ruleRequired],
  },
  don_gia: {
    component: "input-price",
    label: "Đơn giá",
    name: "don_gia",
    placeholder: "Đơn giá",
    rules: [ruleRequired],
  },
  tong_tien: {
    component: "input-price",
    label: "Tổng tiền",
    name: "tong_tien",
    placeholder: "Tổng tiền",
    rules: [ruleRequired],
  },
  nam_sx: {
    component: "date-picker",
    label: "Năm sản xuất",
    name: "nam_sx",
    placeholder: "Năm sản xuất",
    rules: [ruleRequired],
    picker: "year",
  },
  han_sd: {
    component: "date-picker",
    label: "Hạn sử dụng",
    name: "han_sd",
    placeholder: "Hạn sử dụng",
    // rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Trạng thái",
    rules: [ruleRequired],
  },
};
//IModalchiTietDanhSachTaiSanRef
export interface IModalchiTietDanhSachTaiSanRef {
  open: (data?: CommonExecute.Execute.IDanhSachTaiSan) => void;
  close: () => void;
}
//Props
export interface PropsTaiSan {
  doiTuongTaiSanSelected: CommonExecute.Execute.IDoiTuongBaoHiemTaiSan;
}
export const TRANG_THAI = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngưng sử dụng", ma: "K"},
];
///Danh Sách đối tượng
export interface TableDoiTuongDataType {
  key: string;
  sott?: number;
  ma?: string;
  ten?: string;
  stt?: number;
  tinh_thanh?: string;
  phuong_xa?: string;
  dchi?: string;
  latitude?: number;
  longitude?: number;
  ten_tinh?: string;
  ten_phuong_xa?: string;
}
export const tableDoiTuongDSColumn: TableProps<TableDoiTuongDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "sott", key: "sott", width: colWidthByKey.sott},
  {...defaultTableColumnsProps, title: "Mã", dataIndex: "ma", key: "ma", width: 100},
  {...defaultTableColumnsProps, title: "Tên", dataIndex: "ten", key: "ten", width: 200, align: "left"},
  {...defaultTableColumnsProps, title: "Tỉnh/Thành", dataIndex: "ten_tinh", key: "ten_tinh", width: 100},
  {...defaultTableColumnsProps, title: "Phường/Xã", dataIndex: "ten_phuong_xa", key: "ten_phuong_xa", width: 100},
  {...defaultTableColumnsProps, title: "Địa chỉ", dataIndex: "dchi", key: "dchi", width: 200, align: "left"},
  {...defaultTableColumnsProps, title: "Kinh độ", dataIndex: "latitude", key: "latitude", width: 60},
  {...defaultTableColumnsProps, title: "Vĩ độ", dataIndex: "longitude", key: "longitude", width: 60},
];
export type DataIndexDoiTuongDS = keyof TableDoiTuongDataType;
export interface IModalDanhSachDoiTuongTaiSanRef {
  open: (data?: CommonExecute.Execute.IDoiTuong) => void;
  close: () => void;
}
export interface DanhSachDoiTuongProps {
  onSelectDoiTuong: (doiTuong: CommonExecute.Execute.IDoiTuong | null) => void;
}
//FormTimKiemDanhSachDoiTuong
export interface IFormTimKiemDanhSachDoiTuong {
  ten?: IFormInput;
}
export const FormTimKiemDanhSachDoiTuong: IFormTimKiemDanhSachDoiTuong = {
  ten: {
    component: "input",
    label: "Tên đối tượng",
    name: "ten",
    placeholder: "Tên đối tượng",
  },
};
export const defaultFormValueTimKiemDoiTuongTaiSan: ReactQuery.ITimKiemPhanTrangDoiTuongParams = {
  ten: "",
  trang: 1,
  so_dong: 14, // set khác default cho vừa màn hình
};
//Chi  tiết danh sách đối tượng
//FormChiTietDoiTuongds
export interface IFormChiTietDoiTuongDS {
  ma?: IFormInput;
  ten?: IFormInput;
  dchi?: IFormInput;
  tinh_thanh?: IFormInput;
  phuong_xa?: IFormInput;
  latitude?: IFormInput;
  longitude?: IFormInput;
}
export const FormChiTietDoiTuongDS: IFormChiTietDoiTuongDS = {
  ma: {
    component: "input",
    name: "ma",
    label: "Mã đối tượng",
    placeholder: "Mã đối tượng",
    // rules: [ruleRequired],
    disabled: true,
  },
  ten: {
    component: "input",
    label: "Tên",
    name: "ten",
    placeholder: "Tên",
    rules: [ruleRequired],
  },
  dchi: {
    component: "input",
    label: "Địa chỉ",
    name: "dchi",
    placeholder: "Địa chỉ",
    rules: [ruleRequired],
  },
  tinh_thanh: {
    component: "select",
    label: "Tỉnh thành",
    name: "tinh_thanh",
    placeholder: "Tỉnh thành",
    rules: [ruleRequired],
  },
  phuong_xa: {
    component: "select",
    label: "Phường xã",
    name: "phuong_xa",
    placeholder: "Phường xã",
    rules: [ruleRequired],
  },
  latitude: {
    component: "input",
    label: "Kinh độ",
    name: "latitude",
    placeholder: "Kinh độ",
    rules: [ruleRequired],
  },
  longitude: {
    component: "input",
    label: "Vĩ độ",
    name: "longitude",
    placeholder: "Vĩ độ",
    rules: [ruleRequired],
  },
};
//IModalchiTietDoiTuongRef
export interface IModalchiTietDoiTuongRef {
  open: (data?: CommonExecute.Execute.IDoiTuong) => void;
  close: () => void;
}
export interface PropsDoiTuong {}
