#BO_MA_QUYEN_LOI {
  .ant-table-row:hover td {
    background-color: #e8f5e9 !important; //test
  }

  .header-cell-custom {
    background-color: #96bf49 !important;
    // background: linear-gradient(to right, #96bf49, #009a55) !important;
    color: #fff !important;
    font-weight: bold !important;
    text-align: center !important;
    border-radius: 0 !important;
  }

  /* 🔒 Ẩn cả thanh cuộn dọc và ngang */
  .antd-table-hide-scroll .ant-table-body {
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
  }

  // .antd-table-hide-scroll .ant-table-body::-webkit-scrollbar {
  //   display: none;
  //   /* Chrome, Safari */
  // }

  //cố định height cho bảng
  // .ant-table-container {
  //   min-height: 620px;
  // }

  // .ant-table-header {
  //   overflow-y: scroll !important;
  // }
  //style row đạ<PERSON> lý khi được chọn -> highlight background
}
.modal-chon-quyen-loi-cha .header-cell-custom {
  background-color: #96bf49 !important;
  color: #fff !important;
  font-weight: bold !important;
  text-align: center !important;
}

.modal-chon-quyen-loi-cha .custom-row-selected {
  background-color: #96bf49 !important;
}

// .modal-chon-quyen-loi-cha.custom-row-selected.ant-table-row:hover {
//   background-color: rgba(154, 188, 234, 0.5) !important;
// }
.modal-chon-quyen-loi-cha .ant-table-row:hover td {
  background-color: #e8f5e9 !important;
}
.quyen-loi-cha .ant-table-title {
  padding: 0;
  margin-bottom: 8px;
}
