import {createContext, useContext} from "react";
import {DanhMucSanPhamContextProps} from "./index.model";
export const DanhMucSanPhamContext = createContext<DanhMucSanPhamContextProps>({
  loading: false,
  tongSoDong: 0,
  filterParams: {},
  danhSachSanPhamPhanTrang: [],
  listNghiepVu: [],
  setFilterParams: () => {},
  layDanhSachSanPhamPhanTrang: () => {},
  onUpdateDanhMucSanPham: () => Promise.resolve(null),
  layChiTietSanPham: () => Promise.resolve(null),
  listDoiTac: [],
  getListDoiTac: () => Promise.resolve(),
  getListNghiepVu: () => Promise.resolve(),
});
export const useDanhMucSanPhamContext = () => useContext(DanhMucSanPhamContext);
