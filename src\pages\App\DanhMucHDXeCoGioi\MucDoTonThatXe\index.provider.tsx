import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";

import {QuanLyMucDoTonThatXeContext} from "./index.context";
import {IQuanLyMucDoTonThatXeContextProps} from "./index.model";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
import {TableMucDoTonThatXeColumnDataType} from "./index.configs";
import {useCommonExecute} from "@src/services/react-queries";

const QuanLyMucDoTonThatXeProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();

  const [listMucDoTonThatXe, setListMucDoTonThatXe] = useState<Array<CommonExecute.Execute.IMucDoTonThatXe>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangMucDoTonThatXeParams & ReactQuery.IPhanTrang>({ma: "", ten: "", trang_thai: "", trang: 1, so_dong: 20});
  
  //khởi tạo dữ liệu ban đàu
  useEffect(() => {//nè
    initData();
  }, []);

  const getChiTietMucDoTonThatXe = useCallback(
    async (data: TableMucDoTonThatXeColumnDataType) => {
      try {
        console.log("[Provider] getChiTietMucDoTonThatXe được gọi với data:", data);
        
        if (!data.ma) {
          console.log("[Provider] Không có mã để tìm kiếm, data.ma =", data.ma);
          return {} as CommonExecute.Execute.IMucDoTonThatXe;
        }
        
        console.log("[Provider] Gọi API với params:", {
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_MUC_DO_TON_THAT_XE
        });
        
        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.ma,
          actionCode: ACTION_CODE.CHI_TIET_MUC_DO_TON_THAT_XE,
        } as any);
        
        console.log("[Provider] Raw response từ API:", response);
        console.log("[Provider] Response.data:", response.data);
        
        // Xử lý structure response.data.lke
        const responseData = response.data as any; // Type casting để truy cập lke
        if (responseData && responseData.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          const result = responseData.lke[0] as CommonExecute.Execute.IMucDoTonThatXe;
          console.log("[Provider] Trả về data từ lke[0]:", result);
          return result;
        } else {
          console.log("[Provider] Không tìm thấy data trong response.data.lke");
          return {} as CommonExecute.Execute.IMucDoTonThatXe;
        }
      } catch (error) {
        console.log("[Provider] getChiTietMucDoTonThatXe error:", error);
        return {} as CommonExecute.Execute.IMucDoTonThatXe;
      }
    },
    [mutateUseCommonExecute],
  );

  const getListMucDoTonThatXe = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_MUC_DO_TON_THAT_XE,
      } as any);
      if (response.data) {
        setListMucDoTonThatXe(response.data.data);
        setTongSoDong(response.data.tong_so_dong);
      }
    } catch (error) {
      console.log("getListMucDoTonThatXe error ", error);
    }
  }, [mutateUseCommonExecute]);

  useEffect(() => {
    getListMucDoTonThatXe();
  }, [filterParams]);

  const capNhatChiTietMucDoTonThatXe = useCallback(
    async (data: ReactQuery.ICapNhatMucDoTonThatXeParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
          actionCode: ACTION_CODE.UPDATE_MUC_DO_TON_THAT_XE,
        } as any);
        return response.data;
      } catch (error) {
        console.log("capNhatChiTietMucDoTonThatXe err", error);
        // return {} as CommonExecute.Execute.IMucDoTonThatXe;
      }
    },
    [mutateUseCommonExecute],
  );

  //initData chỉ dùng trong useEffect 1 lần, không truyền đi đâu cả, nên không cần memo hoá
  const initData = () => {};

  const value = useMemo<IQuanLyMucDoTonThatXeContextProps>(
    () => ({
      listMucDoTonThatXe,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      filterParams,
      getListMucDoTonThatXe,
      getChiTietMucDoTonThatXe,
      capNhatChiTietMucDoTonThatXe,
      setFilterParams,
    }),
    [listMucDoTonThatXe, tongSoDong, mutateUseCommonExecute, filterParams, getListMucDoTonThatXe, getChiTietMucDoTonThatXe, capNhatChiTietMucDoTonThatXe],
  );

  return <QuanLyMucDoTonThatXeContext.Provider value={value}>{children}</QuanLyMucDoTonThatXeContext.Provider>;
};

export default QuanLyMucDoTonThatXeProvider;
