import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface NhomDoiTuongContextProps {
  danhSachNhomDoiTuong: Array<CommonExecute.Execute.INhomDoiTuong>;
  loading: boolean;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  filterParams: ReactQuery.ITimKiemPhanTrangNhomDoiTuongParams & ReactQuery.IPhanTrang;
  chiTietNhomDoiTuong: CommonExecute.Execute.IChiTietNhomDoiTuong | null;
  // listNhomDoiTuongCT: Array<CommonExecute.Execute.IChiTietNhomDoiTuong>;
  listNhomDoiTuongCT: Array<NonNullable<CommonExecute.Execute.IChiTietNhomDoiTuong["lke_ct"]>[0]>;
  onDeleteNhomDoiTuongCT: (body: ReactQuery.IDeleteNhomDoiTuongCTParams) => Promise<number | null | undefined>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangNhomDoiTuongParams & ReactQuery.IPhanTrang>>;
  getListDoiTac: () => Promise<void>;
  onUpdateNhomDoiTuong: (item: ReactQuery.IUpdateNhomDoiTuongParams) => Promise<number | null | undefined>;
  layDanhSachNhomDoiTuongPhanTrang: (params: ReactQuery.ITimKiemPhanTrangNhomDoiTuongParams) => Promise<{data: Array<CommonExecute.Execute.INhomDoiTuong>; tong_so_dong: number} | null>;
  tongSoDong: number;
  layChiTietNhomDoiTuong: (params: ReactQuery.IChiTietNhomDoiTuongParams) => Promise<CommonExecute.Execute.IChiTietNhomDoiTuong | null>;
  // defaultFormValue: object;
}
