import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

export interface TableTaiKhoanNguoiDungDataType {
  key: string;
  ten?: string;
  ma_doi_tac?: string;
  doi_tac_ten_tat?: string;
  ma_chi_nhanh?: string;
  chi_nhanh_ten_tat?: string;
  phong?: string;
  phong_ten?: string;
  ma?: string;
  stt?: number;
  sott?: number;
  trang_thai?: string;
  trang_thai_ten?: string;
  dthoai?: string;
  email?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  ngay_hl?: string;
  ngay_kt?: string;
  ma_chuc_danh?: string;
  ten_chuc_danh?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const taiKhoanNguoiDungColumns: TableProps<TableTaiKhoanNguoiDungDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "stt",
    key: "stt",
    width: colWidthByKey.sott,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã tài khoản",
    dataIndex: "ma",
    key: "ma",
    width: 120,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên người dùng",
    dataIndex: "ten",
    key: "ten",
    width: 200,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Chức danh",
    dataIndex: "ten_chuc_danh",
    key: "ten_chuc_danh",
    width: 120,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Đơn vị",
    dataIndex: "doi_tac_ten_tat",
    key: "doi_tac_ten_tat",
    width: 150,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Chi nhánh",
    dataIndex: "chi_nhanh_ten_tat",
    key: "chi_nhanh_ten_tat",
    width: 150,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Phòng",
    dataIndex: "phong_ten",
    key: "phong_ten",
    align: "center",
    ...defaultTableColumnsProps,
  },

  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 130,
    align: "center",
    ...defaultTableColumnsProps,
  },
];

export const setFormFields = (form: any, chiTietPhongBan: any) => {
  if (chiTietPhongBan) {
    form.setFields([
      {
        name: "ten",
        value: chiTietPhongBan.ten || "",
      },
      {
        name: "ma",
        value: chiTietPhongBan.ma || "",
      },
      {
        name: "stt",
        value: chiTietPhongBan.stt || "",
      },
      {
        name: "dthoai",
        value: chiTietPhongBan.dthoai || "",
      },
      {
        name: "trang_thai",
        value: chiTietPhongBan.trang_thai,
      },
      {
        name: "ma_doi_tac",
        value: chiTietPhongBan.ma_doi_tac,
      },
      {
        name: "ma_chi_nhanh",
        value: chiTietPhongBan.ma_chi_nhanh,
      },
    ]);
  }
};

//option select trạng thái
export const optionTrangThaiTaiKhoanNguoiDungSelect = [
  {ma: "", ten: "Tất cả"},
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngừng sử dụng"},
];

//Form
export interface IFormTimKiemPhanTrangTaiKhoanNguoiDungFieldsConfig {
  ma_doi_tac: IFormInput;
  ma_chi_nhanh: IFormInput;
  ma: IFormInput;
  nd_tim: IFormInput;

  phong: IFormInput;
}
export const FormTimKiemPhanTrangTaiKhoanNguoiDung: IFormTimKiemPhanTrangTaiKhoanNguoiDungFieldsConfig = {
  ma_doi_tac: {
    component: "select",
    name: "ma_doi_tac",
    label: "Đối tác",
    placeholder: "Chọn mã đối tác",
  },
  ma_chi_nhanh: {
    component: "select",
    name: "ma_chi_nhanh",
    label: "Chi nhánh",
    placeholder: "Chọn chi nhánh",
  },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã tài khoản",
    placeholder: "Nhập mã tài khoản",
  },
  nd_tim: {
    component: "input",
    name: "nd_tim",
    label: "Tìm kiếm thông tin",
    placeholder: "Nhập tên người dùng/tài khoản",
  },
  phong: {
    component: "select",
    name: "phong",
    label: "Phòng ban",
    placeholder: "Chọn phòng ban",
  },
};

export const radioItemTrangThaiTaiKhoanNSDTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];
