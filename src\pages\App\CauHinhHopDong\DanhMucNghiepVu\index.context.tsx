import {createContext, useContext} from "react";
import {DanhMucNghiepVuContextProps} from "./index.model";
export const DanhMucNghiepVuContext = createContext<DanhMucNghiepVuContextProps>({
  loading: false,
  tongSoDong: 0,
  filterParams: {},
  danhSachNghiepVuPhanTrang: [],
  setFilterParams: () => {},
  layDanhSachNghiepVuPhanTrang: () => {},
  onUpdateDanhMucNghiepVu: () => Promise.resolve(null),
  layChiTietNghiepVu: () => Promise.resolve(null),
  listDoiTac: [],
  getListDoiTac: () => Promise.resolve(),
});
export const useDanhMucNghiepVuContext = () => useContext(DanhMucNghiepVuContext);
