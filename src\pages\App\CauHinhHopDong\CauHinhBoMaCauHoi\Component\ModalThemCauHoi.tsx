import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {IModalThemCauHoiRef, FormThemCauHoi, KIEU_CHON, BAT_BUOC, TRANG_THAI, PropsThem, TableCauHoiCTDataType, DataIndexCauHoiCT, cauHoiCTColumns, FormThemCauHoiCT} from "./index.configs";
import {Col, Flex, Form, InputRef, Modal, Row, Table, TableColumnType, Tag} from "antd";
import {useBoMaCauHoiContext} from "../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal, Highlighter, InputCellTable, TableFilterDropdown} from "@src/components";
import {CheckOutlined, PlusCircleOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {FilterDropdownProps} from "antd/es/table/interface";
const {ma, ten, bat_buoc, kieu_chon, do_rong, trang_thai} = FormThemCauHoi;
// const interface Props:{}
const ModalThemCauHoiComponent = forwardRef<IModalThemCauHoiRef, PropsThem>(({CauHoiApDung, onDataChange}: PropsThem, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataThemCauHoi?: CommonExecute.Execute.ICauHoi) => {
      setIsOpen(true);
      if (!dataThemCauHoi) {
        // setDataSource([]);
        setThemCauHoi(null);
      }
      if (dataThemCauHoi) {
        setThemCauHoi(dataThemCauHoi);
        setDisableSubmit(true);
        // layDanhSachCauHoiCT({ma_cau_hoi: dataThemCauHoi?.ma, bt_ap_dung: Number(CauHoiApDung)});
        // setDataSource(danhSachCauHoiCT.map(item => ({...item, key: item.ma_cau_hoi})));
      }
      console.log("dataThemCauHoi", dataThemCauHoi);
    },
    close: () => setIsOpen(false),
  }));
  const [pageSize, setPageSize] = useState(4);
  const [themCauHoi, setThemCauHoi] = useState<CommonExecute.Execute.ICauHoi | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedRowKey, setSelectedRowKey] = useState<string | number | null>(null);
  const [dataSource, setDataSource] = useState<Array<TableCauHoiCTDataType>>([]);
  const {loading, onUpdateCauHoi, layDanhSachCauHoi, danhSachCauHoiCT, selecteCauHoiApDung, setDanhSachCauHoiCT} = useBoMaCauHoiContext();
  const [formThemCauHoiCT] = Form.useForm();
  const [formCauHoi] = Form.useForm();
  const formValues = Form.useWatch([], formCauHoi);
  const [searchedColumnCH, setSearchedColumnCH] = useState("");
  const [searchTextCH, setSearchTextCH] = useState("");
  const searchInputCH = useRef<InputRef>(null);
  const {gia_tri, ten_gia_tri, mac_dinh} = FormThemCauHoiCT;
  const [inputRowKey, setInputRowKey] = useState<string | null>(null);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [disableSubmitCT, setDisableSubmitCT] = useState<boolean>(false);
  useEffect(() => {
    if (themCauHoi) {
      const arrFormData = [];
      for (const key in themCauHoi) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.ICauHoi,
          value: themCauHoi[key as keyof CommonExecute.Execute.ICauHoi],
        });
      }
      formCauHoi.setFields(arrFormData);
    }
  }, [themCauHoi, formCauHoi]);
  useEffect(() => {
    formCauHoi
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [formCauHoi, formValues]);
  useEffect(() => {
    if (CauHoiApDung && themCauHoi) {
      setDisableSubmitCT(false);
    } else {
      setDisableSubmitCT(true);
    }
  }, [CauHoiApDung, themCauHoi]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    formCauHoi.resetFields();
    if (selecteCauHoiApDung !== null && selecteCauHoiApDung !== undefined) {
      layDanhSachCauHoi({bt_ap_dung: Number(selecteCauHoiApDung)});
    }
    setThemCauHoi(null);
    setDanhSachCauHoiCT([]);
  }, [formCauHoi, layDanhSachCauHoi, selecteCauHoiApDung, setDanhSachCauHoiCT]);
  useEffect(() => {
    console.log("themCauHoi", themCauHoi);
  }, [themCauHoi]);
  const setErrorFormFields = useCallback(
    (name: string, errors: string[]) => {
      formCauHoi.setFields([
        {
          name,
          errors,
        },
      ]);
    },
    [formCauHoi],
  );
  const handleSearchCH = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexCauHoiCT) => {
    confirm();
    setSearchTextCH(selectedKeys[0]);
    setSearchedColumnCH(dataIndex);
  }, []);
  const handleResetCH = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexCauHoiCT) => {
      clearFilters();
      setSearchedColumnCH("");
      handleSearchCH([""], confirm, dataIndex);
    },
    [handleSearchCH],
  );

  const renderFormInputColum = (props?: any, span = 6) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const getColumnSearchCauHoiProps = (dataIndex: DataIndexCauHoiCT, title: string): TableColumnType<DataIndexCauHoiCT> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
      <TableFilterDropdown
        ref={searchInputCH}
        title={title}
        selectedKeys={selectedKeys}
        dataIndex={dataIndex}
        setSelectedKeys={setSelectedKeys}
        handleSearch={handleSearchCH}
        confirm={confirm}
        clearFilters={clearFilters}
        handleReset={handleResetCH}
      />
    ),
    // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex as keyof DataIndexCauHoiCT]
        ? record[dataIndex as keyof DataIndexCauHoiCT]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInputCH.current?.select(), 100);
        }
      },
    },
    // filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiNhomTable : undefined,
    render: (text, record, index) => {
      //   if (dataIndex === "trang_thai_ten") {
      //     // Xác định màu dựa vào text (trạng thái hiển thị)
      //     const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
      //     if (record.key.toString().includes("empty")) return "";
      //     return <Tag color={color}>{text}</Tag>;
      //   }
      return searchedColumnCH === dataIndex ? (
        <Highlighter searchWords={[searchTextCH]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {""}
        </Tag>
      );
    },
  });
  const dataTableListCauHoiCT = useMemo<Array<TableCauHoiCTDataType>>(() => {
    try {
      const mappedData = danhSachCauHoiCT.map((item: any, index: number) => ({
        stt: item.sott ?? index + 1,
        bt_ap_dung: item.bt_ap_dung,
        ma_cau_hoi: item.ma_cau_hoi,
        ten_gia_tri: item.ten_gia_tri,
        gia_tri: item.gia_tri,
        mac_dinh: item.mac_dinh,
        ngay_tao: item.ngay_tao,
        nguoi_tao: item.nguoi_tao,
        ngay_cap_nhat: item.ngay_cap_nhat,
        nguoi_cap_nhat: item.nguoi_cap_nhat,
        key: index.toString(),
      }));
      const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);
      setDataSource([...mappedData, ...arrEmptyRow]);
      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng ngày áp dụng:", error);
      return [];
    }
  }, [danhSachCauHoiCT, pageSize]);
  useEffect(() => {
    console.log("dataSource đã thay đổi:", dataSource);
  }, [dataSource]);

  const onConfirm = useCallback(async () => {
    try {
      const values: ReactQuery.IUpdateCauHoiParams = formCauHoi.getFieldsValue(); //lấy ra values của form
      const validRows = dataSource.filter(row => !row.key.includes("empty") && ((row.ten_gia_tri && row.ten_gia_tri.trim() !== "") || (row.gia_tri && row.gia_tri.trim() !== "")));
      const finalValues = {
        ...values,
        bt_ap_dung: Number(CauHoiApDung),
        ch_ct: validRows.map(row => ({
          ten_gia_tri: row.ten_gia_tri,
          gia_tri: row.gia_tri,
          mac_dinh: row.mac_dinh,
        })),
      };
      const response = await onUpdateCauHoi(finalValues);

      // await layDanhSachCauHoi({bt_ap_dung: Number(CauHoiApDung)});
      // const validRows = dataSource.filter(row => row.ten_gia_tri && row.gia_tri);
      // Chỉ lấy dòng thật (có ma_cau_hoi) và có ít nhất 1 trường dữ liệu

      console.log(" validRows", validRows);

      // Sửa: Kiểm tra response đúng cách
      if (response !== undefined && response !== null) {
        console.log("cập nhật thành công");
        setIsOpen(false);
        closeModal();
      } else {
        console.log("cập nhật thất bại");
      }
    } catch (error) {
      console.log("onConfirm", error);
    }
  }, [formCauHoi, dataSource]);

  //render footer
  const renderFooter = () => {
    return (
      <div className="flex justify-between">
        {renderTableCauHoiCTFooter()}

        <Button type="primary" onClick={onConfirm} icon={<CheckOutlined />} disabled={disableSubmit}>
          Lưu
        </Button>
      </div>
    );
  };
  // Render cột cho bảng đơn vị quản lý
  const handleCheckMacDinh = (key: string) => {
    setDataSource(prev => prev.map(item => (item.key === key ? {...item, mac_dinh: "C"} : {...item, mac_dinh: "K"})));
    // Nếu cần callback ra ngoài:
    const filtered = dataSource.map(item => (item.key === key ? {...item, mac_dinh: "C"} : {...item, mac_dinh: "K"})).filter(i => i.mac_dinh === "C" && !i.key.includes("empty"));
    onDataChange?.(filtered);
  };
  const handleInputChange = (index: number, dataIndex: string, value: string) => {
    setDataSource(prev => {
      const next = [...prev];
      next[index] = {...next[index], [dataIndex]: value};
      console.log("next", next);
      return next;
    });
  };
  const handleAddRow = () => {
    // Tìm index dòng trống đầu tiên
    const emptyRowIndex = dataSource.findIndex(item => item.key.includes("empty"));
    const newData = [...dataSource];
    if (emptyRowIndex !== -1) {
      newData.splice(emptyRowIndex, 1);
    }
    // Tìm vị trí cuối cùng của dữ liệu gốc (dòng có ma_cau_hoi hoặc trường phân biệt)
    const lastDataIndex = newData.reduce((lastIdx, item, idx) => (item.ma_cau_hoi ? idx : lastIdx), -1);
    // Tạo dòng dữ liệu mới trống
    const newKey = `new-${Date.now()}`;
    const newRow = {
      bt_ap_dung: Number(CauHoiApDung),
      key: newKey,
      ma_cau_hoi: themCauHoi?.ma ?? "",
      ten_gia_tri: "",
      gia_tri: "",
      mac_dinh: "K",
    };

    // Thêm dòng mới vào ngay sau dòng dữ liệu cuối cùng
    newData.splice(lastDataIndex + 1, 0, newRow);

    setDataSource(newData);
    setInputRowKey(newKey);
  };
  const renderColumn = (column: any, index: number) => {
    if (column.dataIndex === "mac_dinh") {
      return {
        ...column,
        render: (_: any, record: TableCauHoiCTDataType) => {
          const isEmptyRow = record.key.includes("empty");
          if (record.key === inputRowKey) {
            return (
              <div className="custom-checkbox-cell" style={{alignItems: "center", verticalAlign: "middle"}}>
                <FormInput className="!mb-0" component="checkbox" checked={record.mac_dinh === "C"} onChange={() => handleCheckMacDinh(record.key)} />
              </div>
            );
          }
          if (isEmptyRow) return <div style={{}} />;
          return (
            <div className="custom-checkbox-cell" style={{alignItems: "center", verticalAlign: "middle"}}>
              <FormInput className="!mb-0" component="checkbox" checked={record.mac_dinh === "C"} onChange={() => handleCheckMacDinh(record.key)} />
            </div>
          );
        },
      };
    }
    if (column.dataIndex === "ten_gia_tri" || column.dataIndex === "gia_tri") {
      return {
        ...column,
        render: (_: any, record: TableCauHoiCTDataType, index: number) => {
          const isEmptyRow = record.key.includes("empty");
          // Nếu là dòng trống đang được chọn để nhập liệu
          if (record.key === inputRowKey) {
            return (
              <div className="custom-checkbox-cell">
                <InputCellTable
                  className="text-left"
                  component="input"
                  key={`${record.key}-${column.dataIndex}`}
                  value={record[column.dataIndex as keyof TableCauHoiCTDataType] ?? ""}
                  index={index}
                  dataIndex={column.dataIndex}
                  onChange={handleInputChange}
                  autoFocus
                />
              </div>
            );
          }
          // Các dòng trống khác chỉ để trống
          if (isEmptyRow) return <div style={{height: 22}} />;
          // Dòng thường thì render như cũ
          return (
            <div className="custom-checkbox-cell">
              <InputCellTable
                className="text-left"
                component="input"
                key={`${column.dataIndex}`}
                value={record[column.dataIndex as keyof TableCauHoiCTDataType] ?? ""}
                index={index}
                dataIndex={column.dataIndex}
                onChange={handleInputChange}
              />
            </div>
          );
        },
      };
    }
    return {
      ...column,
      ...(column.key && typeof column.title === "string" && column.key !== "stt" ? getColumnSearchCauHoiProps(column.key, column.title) : {}),
      render: (text: string) => text || "", // Đảm bảo hiển thị chuỗi rỗng nếu text undefined
    };
  };
  const renderTableCauHoiCT = () => {
    return (
      <Table<TableCauHoiCTDataType>
        className="table-nhom no-header-border-radius"
        {...defaultTableProps}
        style={{cursor: "pointer"}}
        title={null}
        pagination={false}
        columns={(cauHoiCTColumns || []).map(renderColumn)}
        dataSource={dataSource}
        bordered
        scroll={dataSource.length > pageSize ? {y: 125} : undefined}
      />
    );
  };
  const renderTableCauHoiCTFooter = () => {
    return (
      <Button className="" type="primary" icon={<PlusCircleOutlined />} onClick={handleAddRow}>
        Thêm chi tiết câu hỏi
      </Button>
    );
  };
  const renderForm = () => (
    <Form form={formCauHoi} layout="vertical">
      {/* MÃ */}
      <Row gutter={16}>
        {renderFormInputColum({...ma, disabled: true})}
        {renderFormInputColum({...ten}, 18)}
        {renderFormInputColum({
          ...kieu_chon,
          options: KIEU_CHON,
          // disabled: themCauHoi ? true : false,
        })}
        {renderFormInputColum({
          ...bat_buoc,
          options: BAT_BUOC,
        })}
        {renderFormInputColum({
          ...do_rong,
        })}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI})}
      </Row>
    </Form>
  );
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={themCauHoi ? "Chi tiết câu hỏi " : "Thêm mới câu hỏi "}
            // trang_thai_ten={themCauHoi?.trang_thai_ten}
            // trang_thai={themCauHoi?.trang_thai}
          />
        }
        className="modal-them-cau-hoi-ct [&_.ant-space]:w-full"
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "55%",
          sm: "55%",
          md: "55%",
          lg: "55%",
          xl: "55%",
          xxl: "55%",
        }}
        style={{top: 130}}
        footer={renderFooter}>
        {renderForm()}
        {renderTableCauHoiCT()}
      </Modal>
    </Flex>
  );
});
ModalThemCauHoiComponent.displayName = "ModalThemCauHoiComponent";
export const ModalThemCauHoi = memo(ModalThemCauHoiComponent, isEqual);
