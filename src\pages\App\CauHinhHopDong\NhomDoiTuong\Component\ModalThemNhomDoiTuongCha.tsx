import {CheckCircleOutlined, ClearOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Flex, Form, Input, InputRef, Modal, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useImperativeHandle, useMemo, useRef, useState} from "react";
import {defaultFormValue, FormTimKiemNhomDoiTuong, TRANG_THAI} from "../index.configs";
import {useNhomDoiTuongContext} from "../index.context";
import "../index.default.scss";
import {NhomDoiTuongChaProps, NhomDoiTuongChaColumns, DataIndexCha, IModalThemNhomDoiTuongChaRef, TableNhomDoiTuongChaColumnDataIndex, TableNhomDoiTuongChaDataType} from "./index.configs";
const {ma, ten} = FormTimKiemNhomDoiTuong;
const ModalThemNhomDoiTuongChaComponent = forwardRef<IModalThemNhomDoiTuongChaRef, NhomDoiTuongChaProps>(({onSelectNhomDoiTuongCha, chiTietNhomDoiTuong}: NhomDoiTuongChaProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: () => {
      setIsOpen(true);
      initData();
    },
    close: () => setIsOpen(false),
  }));
  const initData = async () => {
    try {
      // await layDanhSachNhomDoiTuongPhanTrang;
      await onSearchApi(defaultFormValue);
    } catch (error) {
      console.log("error", error);
    }
  };
  const {layDanhSachNhomDoiTuongPhanTrang, loading} = useNhomDoiTuongContext();

  const [nhomDoiTuongChaSelected, setNhomDoiTuongChaSelected] = useState<CommonExecute.Execute.INhomDoiTuong | null>(null);
  // const [tongSoDongNhomDoiTuongCha, setTongSoDongNhomDoiTuongCha] = useState<number>(0);
  const [danhSachNhomDoiTuong, setDanhSachNhomDoiTuong] = useState<Array<CommonExecute.Execute.INhomDoiTuong>>([]);
  const [tongSoDongNhomDoiTuong, setTongSoDongNhomDoiTuong] = useState<number>(0);
  const [IsOpen, setIsOpen] = useState(false);
  const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangNhomDoiTuongParams>(defaultFormValue);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(7);
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState<TableNhomDoiTuongChaColumnDataIndex | "">("");
  const searchInput = useRef<InputRef>(null);
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexCha) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);
  const handleReset = useCallback((clearFilters: () => void, confirm: () => void, dataIndex: DataIndexCha) => {
    clearFilters();
    confirm();
    setSearchText("");
    setSearchedColumn(dataIndex);
  }, []);

  const dataTableListNhomDoiTuong = useMemo<Array<TableNhomDoiTuongChaDataType>>(() => {
    try {
      const filteredData = danhSachNhomDoiTuong.filter(item => item.trang_thai === "D");
      console.log("filteredData nhóm đối tượng cha", filteredData);
      setTongSoDongNhomDoiTuong(filteredData.length);
      const tableData = filteredData.map((item: any, index: number) => {
        return {
          // stt: item.stt ?? index + 1,
          stt: index + 1,
          ma: item.ma,
          ten: item.ten,
          ma_cha: item.ma_cha,
          ma_dau: item.ma_dau,
          trang_thai: item.trang_thai,
          trang_thai_ten: item.trang_thai_ten,

          key: index.toString(),
        };
      });
      const arrEmptyRow: Array<TableNhomDoiTuongChaDataType> = fillRowTableEmpty(tableData.length, 10);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListNhomDoiTuong error", error);
      return [];
    }
  }, [danhSachNhomDoiTuong]);

  //Bấm tìm kiếm
  const onSearchApi = async (values: ReactQuery.ITimKiemPhanTrangNhomDoiTuongParams & ReactQuery.IPhanTrang) => {
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,

      ma: values.ma ?? "",
      ten: values.ten ?? "",

      trang_thai: "D", // Gửi trạng thái D lên API
      trang: values.trang ?? 1,
      so_dong: values.so_dong ?? pageSize,
    };
    console.log("cleanedValues", cleanedValues);
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    const result = await layDanhSachNhomDoiTuongPhanTrang({...cleanedValues, trang: 1, so_dong: pageSize});
    if (result) {
      setDanhSachNhomDoiTuong(result.data);
      setTongSoDongNhomDoiTuong(result.tong_so_dong);
    } else {
      setDanhSachNhomDoiTuong([]);
      setTongSoDongNhomDoiTuong(0);
    }
    console.log("danh sách nhóm đối tượng ", result?.data);
  };
  //Bấm xác nhận chọn
  const onPressXacNhan = () => {
    try {
      if (nhomDoiTuongChaSelected) {
        const formattedNhomDoiTuongCha = {
          ten: nhomDoiTuongChaSelected.ten || "",
          ma: nhomDoiTuongChaSelected.ma || "",
        };
        console.log("formattedNhomDoiTuongCha", nhomDoiTuongChaSelected);
        onSelectNhomDoiTuongCha(nhomDoiTuongChaSelected);
        // console.log("Formatted nhomDoiTuongChaSelected", formattedNhomDoiTuongCha);
      } else {
        onSelectNhomDoiTuongCha(null);
      }
      setIsOpen(false);
    } catch (error) {
      console.log("onPressXacNhan error", error);
    }
  };
  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    async (page: number, pageSize: number) => {
      try {
        setPage(page);
        setPageSize(pageSize);
        const result = await layDanhSachNhomDoiTuongPhanTrang({...searchParams, trang: page, so_dong: pageSize});
        if (result) {
          setDanhSachNhomDoiTuong(result.data);
          setTongSoDongNhomDoiTuong(result.tong_so_dong);
        } else {
          setDanhSachNhomDoiTuong([]);
          setTongSoDongNhomDoiTuong(0);
        }
      } catch (error) {
        console.log("onChangePage error:", error);
        setDanhSachNhomDoiTuong([]);
        setTongSoDongNhomDoiTuong(0);
      }
    },
    [searchParams],
  ); //tạo cột tìm kiếm
  //   const nhomDoiTuongChaSelected = useRef<CommonExecute.Execute.INhomDoiTuong | null>(null);
  const getColumnSearchProps = (dataIndex: DataIndexCha, title: string): TableColumnType<TableNhomDoiTuongChaDataType> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
      <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="header-cell-custom flex flex-row items-center p-2">
        <Input
          ref={searchInput}
          placeholder={`Tìm theo ${title}`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
          style={{display: "block", marginRight: 8}}
        />
        <Tooltip title="Tìm kiếm">
          <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
        </Tooltip>
        <Tooltip title="Xoá">
          <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
        </Tooltip>
      </div>
    ),
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    render: (text, record, index) => {
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  const renderTable = () => {
    return (
      <Table<TableNhomDoiTuongChaDataType>
        {...defaultTableProps}
        // bordered={false}
        // sticky
        className="doi-tuong-cha no-header-border-radius"
        // scroll={{x: "max-content,y: 100vh"}}
        loading={loading}
        // style={{cursor: "pointer"}}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"},
            onClick: () => {
              if (record.key.toString().includes("empty")) return;
              setNhomDoiTuongChaSelected(record as CommonExecute.Execute.INhomDoiTuong);
            },
            onDoubleClick: () => onPressXacNhan(),
          };
        }}
        columns={(NhomDoiTuongChaColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableNhomDoiTuongChaDataType, item.title) : {}),
          };
        })} //định nghĩa cột của table
        rowClassName={record => (record.ma && record.ma === nhomDoiTuongChaSelected?.ma ? "custom-row-selected" : "")}
        dataSource={dataTableListNhomDoiTuong}
        // title={renderHeaderTableNhomDoiTuongCha}
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDongNhomDoiTuong,
          defaultPageSize: pageSize,
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
          // showLessItems: true, //Hiển thị ít mục trang hơn
          // defaultPageSize: 2, //Số lượng item dữ liệu mặc định trên mỗi trang
          // onShowSizeChange : Được gọi khi pageSize thay đổi
          // showTitle: false,// Hiển thị tiêu đề của item trang
          //showTotal : hiển thị UI bao nhiêu / bao nhiêu bản ghi
        }}
      />
    );
  };
  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  //render header table
  const renderHeaderTableNhomDoiTuongCha = () => {
    return (
      <div
        style={{
          margin: "16px 0",
        }}>
        <Form initialValues={{trang_thai: TRANG_THAI[0].ma}} layout="vertical" className="[&_.ant-form-item]:!mb-0" onFinish={onSearchApi}>
          <div className="flex flex-col gap-5">
            <Row gutter={16} align={"bottom"}>
              {/* {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac})} */}
              {renderFormInputColum(ma)}
              {renderFormInputColum(ten)}
              {/* {renderFormInputColum({...trang_thai, options: TRANG_THAI_CHI_TIET_DAI_LY})} */}
              <Col span={4}>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="mb-0 w-full">
                  Tìm kiếm
                </Button>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
    );
  };

  const renderFooter = () => {
    return (
      //   <Form.Item>
      <Tooltip title={nhomDoiTuongChaSelected ? "" : "Vui lòng chọn nhóm đối tượng cha"}>
        <Button type={"primary"} onClick={() => onPressXacNhan()} className="mr-2" iconPosition="end" icon={<CheckCircleOutlined />} disabled={nhomDoiTuongChaSelected ? false : true}>
          Chọn
        </Button>
      </Tooltip>
      //   </Form.Item>
    );
  };
  return (
    <Flex vertical gap="middle" align="flex-start ">
      <Modal
        maskClosable={false}
        title="Chọn nhóm đối tượng cha"
        // centered
        // bodyStyle={{maxHeight: "70vh", overflowY: "auto"}}
        className="modal-chon-doi-tuong-cha"
        open={IsOpen}
        onOk={() => setIsOpen(false)}
        onCancel={() => setIsOpen(false)}
        width={{
          xs: "50%",
          sm: "50%",
          md: "50%",
          lg: "50%",
          xl: "50%",
          xxl: "50%",
        }}
        style={{top: 50}}
        footer={renderFooter}>
        {renderHeaderTableNhomDoiTuongCha()}
        {renderTable()}
      </Modal>
    </Flex>
  );
});
//   const tableHeight = useTableHeight(["footer", "header"]);
ModalThemNhomDoiTuongChaComponent.displayName = "ModalThemNhomDoiTuongChaComponent";
export const ModalThemNhomDoiTuongCha = memo(ModalThemNhomDoiTuongChaComponent, isEqual);
