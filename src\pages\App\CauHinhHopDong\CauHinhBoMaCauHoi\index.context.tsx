import {createContext, useContext} from "react";
import {BoMaCauHoiContextProps} from "./index.model";
export const BoMaCauHoiContext = createContext<BoMaCauHoiContextProps>({
  loading: false,
  tongSoDong: 0,
  filterParams: {},
  danhSachSanPhamPhanTrang: [],
  danhSachCauHoiApDung: [],
  danhSachCauHoi: [],
  danhSachCauHoiCT: [],
  selecteCauHoiApDung: null,
  listNghiepVu: [],
  getListNghiepVu: () => Promise.resolve(),
  setSelectedCauHoiApDung: () => {},
  // layDanhSachCauHoiCT: () => Promise.resolve([]),
  setDanhSachCauHoiCT: () => {},
  // onUpdateCauHoiCT: () => Promise.resolve(null),
  layChiTietCauHoi: () => Promise.resolve(null),
  setDanhSachCauHoi: () => {},
  setDanhSachCauHoiApDung: () => {},
  layDanhSachCauHoi: () => Promise.resolve([]),
  onUpdateCauHoi: () => Promise.resolve(null),
  onDeleteCauHoi: () => Promise.resolve(),
  layDanhSachCauHoiApDung: () => {},
  onUpdateCauHoiApDung: () => Promise.resolve(null),
  onDeleteCauHoiApDung: () => Promise.resolve(),
  layDanhSachSanPhamPhanTrang: () => {},
  setFilterParams: () => {},
  layChiTietSanPham: () => Promise.resolve(null),
  listDoiTac: [],
  getListDoiTac: () => Promise.resolve(),
});
export const useBoMaCauHoiContext = () => useContext(BoMaCauHoiContext);
