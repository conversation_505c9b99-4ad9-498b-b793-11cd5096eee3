import {ClearOutlined, SearchOutlined} from "@ant-design/icons";
import {Checkbox, FormInput, Highlighter} from "@src/components";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Button, Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {useCallback, useEffect, useMemo, useRef, useState} from "react";
import {
  DataIndexChucNang,
  DataIndexVaiTro,
  FormTimKiemVaiTroChucNang,
  phanQuyenChucNangColumns,
  radioItemTrangThaiVaiTroTable,
  TableChucNangNguoiDungDataType,
  TableVaiTroDataType,
  vaiTroColumns,
} from "./index.configs";
import {useQuanLyTaiKhoanNguoiDungContext} from "../index.context";
import {IFormInput, ReactQuery} from "@src/@types";
import {COLOR_PALETTE} from "@src/constants";

interface TabPhanQuyenChucNangTheoVaiTroProps {
  chiTietNguoiSuDung: CommonExecute.Execute.IChiTietNguoiSuDung | null;
  onDataChange?: (data: TableChucNangNguoiDungDataType[]) => void;
  onVaiTroChange?: (data: TableVaiTroDataType[]) => void;
}

const TabPhanQuyenChucNangTheoVaiTro: React.FC<TabPhanQuyenChucNangTheoVaiTroProps> = (props: TabPhanQuyenChucNangTheoVaiTroProps) => {
  const {chiTietNguoiSuDung, onDataChange} = props;
  const {loading, loadingVT, layDanhSachVaiTroChucNangPhanTrang, danhSachVaiTroChucNang, layChucNangTheoVaiTro, setChucNangSelected, chucNangSelected, tongSoDongVaiTro} =
    useQuanLyTaiKhoanNguoiDungContext();

  const [formTimKiemVaiTroChucNang] = Form.useForm();
  const [searchedColumn, setSearchedColumn] = useState("");
  const [searchText, setSearchText] = useState("");
  const [dataSource, setDataSource] = useState<TableChucNangNguoiDungDataType[]>([]);
  const [chucNangSearchText, setChucNangSearchText] = useState("");
  const {ten} = FormTimKiemVaiTroChucNang;
  // const [totalItems, setTotalItems] = useState(0);
  const searchInput = useRef<InputRef>(null);
  const searchInputVT = useRef<InputRef>(null);
  const [selectedVaiTro, setSelectedVaiTro] = useState<string | null>(null);
  const [rawChucNangData, setRawChucNangData] = useState<any[]>([]);
  // const [rawChucNangData, setRawChucNangData] = useState<CommonExecute.Execute.IChiTietNguoiSuDung.nsd_quyen>([]);
  const [isAllChecked, setIsAllChecked] = useState(false);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangVaiTroChucNangParams & ReactQuery.IPhanTrang>({
    ma: "",
    ten: "",
    trang_thai: "",
    trang: 1,
    so_dong: 8,
  });
  const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangVaiTroChucNangParams>(filterParams);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(8);
  useEffect(() => {
    const cleanedValues = {
      ma: filterParams.ma ?? "",
      ten: filterParams.ten ?? "",
      trang_thai: filterParams.trang_thai ?? "",
      trang: page,
      so_dong: pageSize,
    };
    setSearchParams(cleanedValues);
    layDanhSachVaiTroChucNangPhanTrang(cleanedValues);
  }, []);
  console.log("tổng số dòng vai trò", tongSoDongVaiTro);

  useEffect(() => {
    console.log("chúc năng được chọn", chucNangSelected);
  }, [chucNangSelected]);
  useEffect(() => {
    const validRows = dataSource.filter(row => !row.key.includes("empty"));
    const allChecked = validRows.length > 0 && validRows.every(row => row.is_checked === "1");
    setIsAllChecked(allChecked);
  }, [dataSource]);

  const onSearchApi = (values: ReactQuery.ITimKiemPhanTrangVaiTroChucNangParams & ReactQuery.IPhanTrang) => {
    const cleanedValues = {
      ...values,
      ma: values.ma ?? "",
      ten: values.ten ?? "",
      trang_thai: values.trang_thai ?? "",
    };
    setSearchParams(cleanedValues);
    setPage(1);
    layDanhSachVaiTroChucNangPhanTrang({...cleanedValues, trang: 1, so_dong: pageSize});
  };

  const handleSelectAll = (checked: boolean) => {
    setDataSource(prevDataSource => {
      const updated = prevDataSource.map(item => {
        if (item.key.includes("empty")) return item;
        return {...item, is_checked: checked ? "1" : "0"};
      });
      console.log("Updated dataSource:", updated); // Log để kiểm tra
      const filtered = updated.filter(i => i.is_checked === "1" && !i.key.includes("empty"));
      onDataChange?.(filtered);
      setChucNangSelected(prev => {
        // Giữ lại các chức năng đã chọn từ vai trò khác
        const otherSelected = prev.filter(p => !rawChucNangData.some(r => r.ma_chuc_nang === p.ma_chuc_nang));
        if (checked) {
          // Thêm tất cả chức năng của vai trò hiện tại
          const newSelected = filtered.map(item => ({
            is_checked: parseInt(item.is_checked) || 0,
            ma_chuc_nang: item.ma_chuc_nang,
          }));
          return [...otherSelected, ...newSelected];
        } else {
          // Xóa tất cả chức năng của vai trò hiện tại
          return otherSelected;
        }
      });
      return updated;
    });
    setIsAllChecked(checked);
  };

  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      setPage(page);
      setPageSize(pageSize);
      layDanhSachVaiTroChucNangPhanTrang({...searchParams, trang: page, so_dong: pageSize});
    },
    [searchParams],
  );

  const dataTableListChucNang = useMemo<Array<TableChucNangNguoiDungDataType>>(() => {
    try {
      const mappedData = rawChucNangData.map((item: any, index: number) => ({
        stt: item.stt ?? index + 1,
        ma_chuc_nang: item.ma_chuc_nang,
        ten: item.ten,
        loai: item.loai,
        is_checked: (chucNangSelected.find(cn => cn.ma_chuc_nang === item.ma_chuc_nang)?.is_checked || 0).toString(),
        key: `${item.ma_chuc_nang}`,
      }));
      console.log("mappdatalist chúc năng", mappedData);
      // const filteredData = chucNangSearchText
      //   ? mappedData.filter(item => Object.values(item).some(value => typeof value === "string" && value.toLowerCase().includes(chucNangSearchText.toLowerCase())))
      //   : mappedData;
      if (mappedData.length === 0 && chucNangSearchText) {
        return [{key: "empty", ten: "Không tìm thấy dữ liệu", stt: "", ma_chuc_nang: "", loai: "", is_checked: "0"}];
      }
      const arrEmptyRow = fillRowTableEmpty(mappedData.length, 8);
      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("dataTableListTableChucNangNguoiDungDataType error", error);
      return [{key: "error", ten: "Lỗi khi tải dữ liệu", stt: "", ma_chuc_nang: "", loai: "", is_checked: "0"}];
    }
  }, [rawChucNangData, chucNangSelected]);

  useEffect(() => {
    setDataSource(dataTableListChucNang);
  }, [dataTableListChucNang]);

  const dataTableListVaiTro = useMemo<Array<TableVaiTroDataType>>(() => {
    try {
      const mappedData = danhSachVaiTroChucNang.map((item: any, index: number) => ({
        stt: item.sott ?? index + 1,
        ma: item.ma || "",
        ten: item.ten || "",
        trang_thai: item.trang_thai || "",
        trang_thai_ten: item.trang_thai_ten || "",
        key: index.toString(),
      }));
      const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);
      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng Vai Trò:", error);
      return [];
    }
  }, [danhSachVaiTroChucNang, pageSize]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexChucNang) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexChucNang) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  const handleSearchVT = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexVaiTro) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleResetVT = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexVaiTro) => {
      clearFilters();
      setSearchedColumn("");
      handleSearchVT([""], confirm, dataIndex);
    },
    [handleSearchVT],
  );

  const handleRowClick = (record: TableChucNangNguoiDungDataType) => {
    if (!record.ma_chuc_nang || !record.key) {
      return;
    }
    const newChonValue = record.is_checked === "1" ? "0" : "1";
    handleInputChange(record.key, "is_checked", newChonValue);
  };

  const handleInputChange = (key: string, field: keyof TableChucNangNguoiDungDataType, value: any) => {
    if (!key) {
      return;
    }
    setDataSource(prevDataSource => {
      const updated = prevDataSource.map(item => (item.key === key ? {...item, [field]: value} : item));
      const filtered = updated.filter(i => i.is_checked === "1" && !i.key.includes("empty"));
      onDataChange?.(filtered);
      setChucNangSelected(prev => {
        if (value === "1") {
          // Thêm hoặc cập nhật chức năng khi được chọn
          return [
            ...prev.filter(p => p.ma_chuc_nang !== key), // Loại bỏ nếu đã tồn tại
            {is_checked: 1, ma_chuc_nang: key},
          ];
        } else {
          // Xóa chức năng khi bỏ chọn
          return prev.filter(p => p.ma_chuc_nang !== key);
        }
      });
      return updated;
    });
  };

  const handleChucNangSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setChucNangSearchText(e.target.value);
  };

  const getColumnSearchProps = (dataIndex: DataIndexChucNang, title: string): TableColumnType<TableChucNangNguoiDungDataType> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
      <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
        <Input
          ref={searchInput}
          placeholder={`Tìm theo ${title}`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
          style={{display: "block", marginRight: 8}}
        />
        <Tooltip title="Tìm kiếm">
          <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
        </Tooltip>
        <Tooltip title="Xoá">
          <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
        </Tooltip>
      </div>
    ),
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    render: (text, record, index) => {
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  const getColumnSearchVaiTroProps = (dataIndex: DataIndexVaiTro, title: string): TableColumnType<TableVaiTroDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInputVT}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearchVT(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearchVT(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleResetVT(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInputVT.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiVaiTroTable : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return <Tag color={color}>{text}</Tag>;
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {/* {"\u00A0"} */}
        </Tag>
      );
    },
  });

  const handleVaiTroRowClick = async (record: TableVaiTroDataType) => {
    if (!record.ma) {
      console.warn("Hàng vai trò không hợp lệ, bỏ qua sự kiện nhấp:", record);
      return;
    }
    setSelectedVaiTro(record.ma);
    try {
      const chucNangData = await layChucNangTheoVaiTro({ma_vai_tro: record.ma});
      console.log("API response:", chucNangData);
      const updatedChucNangData = Array.isArray(chucNangData)
        ? chucNangData.map((item: any) => ({
            ...item,
            is_checked: chucNangSelected.find(cn => cn.ma_chuc_nang === item.ma_chuc_nang)?.is_checked || "0",
          }))
        : [];
      setRawChucNangData(updatedChucNangData);
    } catch (error) {
      console.error("Lỗi khi lấy dữ liệu chức năng cho vai trò:", error);
      setRawChucNangData([]);
    }
  };

  const renderColumn = (column: any) => {
    if (column.dataIndex === "is_checked") {
      return {
        ...column,
        title: (
          <div style={{height: 19, alignItems: "center", verticalAlign: "midle"}}>
            <Checkbox checked={isAllChecked} onChange={e => handleSelectAll(e.target.checked)}></Checkbox>
          </div>
        ),
        render: (_: any, record: TableChucNangNguoiDungDataType) => {
          const isEmptyRow = !record.ma_chuc_nang;
          if (isEmptyRow) {
            return <div style={{height: 17}} />;
          }
          return (
            <div className="custom-checkbox-cell" style={{height: 17, alignItems: "center", verticalAlign: "midle"}}>
              <FormInput
                className="!mb-0"
                component="checkbox"
                checked={record.is_checked === "1"}
                disabled={record.key.includes("empty")}
                onChange={e => {
                  if (!record.key) {
                    return;
                  }
                  handleInputChange(record.key, "is_checked", e.target.checked ? "1" : "0");
                }}
              />
            </div>
          );
        },
      };
    }
    return {
      ...column,
      ...(column.key && typeof column.title === "string" && column.key !== "stt" ? getColumnSearchProps(column.key, column.title) : {}),
      render: (text: string) => text || "\u00A0",
    };
  };

  const renderFormInputColum = (props: IFormInput, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  const renderHeaderTableVaiTroChucNang = () => {
    return (
      <div className="" style={{marginBottom: "16px"}}>
        <Form form={formTimKiemVaiTroChucNang} initialValues={{ten: ""}} layout="vertical" className="[&_.ant-form-item]:!mb-0" onFinish={onSearchApi}>
          <div>
            <Row gutter={16} align={"bottom"}>
              {renderFormInputColum({
                ...ten,
              })}
              <Col span={4}>
                <Form.Item>
                  <Button type="primary" htmlType="submit" loading={loadingVT} icon={<SearchOutlined />} className="mb-0 w-full">
                    Tìm kiếm
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
    );
  };

  return (
    <Row>
      <Col span={12} className="table-left w-50 pr-2">
        {renderHeaderTableVaiTroChucNang()}
        <Table<TableVaiTroDataType>
          className="table-vai-tro no-header-border-radius"
          {...defaultTableProps}
          loading={loadingVT}
          onRow={(record, rowIndex) => {
            return {
              style: {
                cursor: "pointer",
                background: record.ma === selectedVaiTro ? "#96BF49" : undefined,
              },
              onClick: () => handleVaiTroRowClick(record),
            };
          }}
          rowClassName={(record, index) => (record.key.includes("empty") ? "empty-row" : "")}
          title={null}
          pagination={{
            ...defaultPaginationTableProps,
            total: tongSoDongVaiTro,
            pageSize: pageSize,
            onChange: (page, pageSize) => {
              onChangePage(page, pageSize);
            },
          }}
          columns={(vaiTroColumns || []).map(item => {
            return {
              ...item,
              ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchVaiTroProps(item.key as keyof TableVaiTroDataType, item.title) : {}),
            };
          })}
          dataSource={dataTableListVaiTro}
          bordered
        />
      </Col>
      <Col span={12} className="table-right w-50">
        <Input placeholder="Tìm kiếm chức năng" value={chucNangSearchText} onChange={handleChucNangSearch} style={{marginBottom: 16, width: "33%"}} prefix={<SearchOutlined />} />
        <Table<TableChucNangNguoiDungDataType>
          key={selectedVaiTro} // Buộc re-render khi vai trò thay đổi
          className="table-chuc-nang no-header-border-radius"
          {...defaultTableProps}
          style={{
            borderBottom: "1px solid #f0f0f0",
            marginBottom: "1rem",
          }}
          onRow={record => ({
            style: {cursor: loadingVT ? "progress" : "pointer"},
            onClick: () => handleRowClick(record),
          })}
          loading={loadingVT}
          columns={(phanQuyenChucNangColumns || []).map(renderColumn)}
          dataSource={dataSource.filter(item => Object.values(item).some(value => typeof value === "string" && value.toLowerCase().includes(chucNangSearchText.toLowerCase())))}
          pagination={false}
          scroll={dataSource.filter(item => !item.key.includes("empty")).length > 8 ? {y: 250} : undefined}
          // scroll={{y: 250}}
        />
      </Col>
    </Row>
  );
};

export default TabPhanQuyenChucNangTheoVaiTro;
